<?php

namespace App\Services;

use App\Models\Download;
use App\Models\Business;
use App\Models\Poster;
use App\Models\Frame;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageGenerationService
{
    private $imageManager;
    
    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }
    
    /**
     * Generate image from design data.
     */
    public function generateImage(Download $download): bool
    {
        try {
            $download->update(['status' => 'processing']);

            $designData = $download->design_data;
            $canvasWidth = 800;
            $canvasHeight = 800;

            // Create base canvas
            $image = $this->imageManager->create($canvasWidth, $canvasHeight);
            $image->fill('#ffffff'); // White background
            
            // Add template background if exists
            if ($download->template_id && $download->template_type) {
                $this->addTemplateBackground($image, $download);
            }
            
            // Add design elements
            if (isset($designData['elements']) && is_array($designData['elements'])) {
                foreach ($designData['elements'] as $element) {
                    $this->addElement($image, $element, $download);
                }
            }
            
            // Generate filename
            $filename = $this->generateFilename($download);
            $filePath = 'downloads/' . $filename;
            
            // Save image
            $format = strtolower($download->file_format);
            $quality = 90;
            
            switch ($format) {
                case 'jpg':
                case 'jpeg':
                    $encodedImage = $image->toJpeg($quality);
                    break;
                case 'png':
                default:
                    $encodedImage = $image->toPng();
                    break;
            }
            
            Storage::put($filePath, $encodedImage);
            $fileSize = Storage::size($filePath);
            
            // Update download record
            $download->markAsCompleted($filePath, $fileSize);
            
            return true;
            
        } catch (\Exception $e) {
            $download->markAsFailed($e->getMessage());
            return false;
        }
    }
    
    /**
     * Add template background to image.
     */
    private function addTemplateBackground($image, Download $download)
    {
        try {
            $template = null;
            
            if ($download->template_type === 'poster') {
                $template = Poster::find($download->template_id);
            } elseif ($download->template_type === 'frame') {
                $template = Frame::find($download->template_id);
            }
            
            if ($template && $template->image_path) {
                $templatePath = storage_path('app/public/' . $template->image_path);
                if (file_exists($templatePath)) {
                    $templateImage = $this->imageManager->read($templatePath);
                    $templateImage->resize(800, 800);
                    $image->place($templateImage, 'top-left', 0, 0);
                }
            }
        } catch (\Exception $e) {
            // Log error but continue with generation
            \Log::warning('Failed to add template background: ' . $e->getMessage());
        }
    }
    
    /**
     * Add design element to image.
     */
    private function addElement($image, array $elementData, Download $download)
    {
        try {
            $type = $elementData['type'] ?? '';
            $style = $this->parseStyle($elementData['style'] ?? '');
            $innerHTML = $elementData['innerHTML'] ?? '';
            
            $x = (int) ($style['left'] ?? 0);
            $y = (int) ($style['top'] ?? 0);
            
            switch ($type) {
                case 'business-logo':
                    $this->addBusinessLogo($image, $x, $y, $download->business);
                    break;
                    
                case 'business-name':
                case 'business-slogan':
                case 'business-phone':
                case 'business-email':
                case 'business-website':
                case 'business-address':
                case 'custom-text':
                    $this->addTextElement($image, $x, $y, $innerHTML, $style, $type, $download->business);
                    break;
            }
        } catch (\Exception $e) {
            // Log error but continue with other elements
            \Log::warning('Failed to add element: ' . $e->getMessage());
        }
    }
    
    /**
     * Add business logo to image.
     */
    private function addBusinessLogo($image, int $x, int $y, ?Business $business)
    {
        if (!$business || !$business->logo_path) {
            return;
        }
        
        try {
            $logoPath = storage_path('app/public/' . $business->logo_path);
            if (file_exists($logoPath)) {
                $logo = $this->imageManager->read($logoPath);
                $logo->resize(80, 80, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
                
                $image->place($logo, 'top-left', $x, $y);
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to add business logo: ' . $e->getMessage());
        }
    }
    
    /**
     * Add text element to image.
     */
    private function addTextElement($image, int $x, int $y, string $innerHTML, array $style, string $type, ?Business $business)
    {
        // Extract text content from HTML
        $text = $this->extractTextFromHtml($innerHTML, $type, $business);
        
        if (empty($text)) {
            return;
        }
        
        // Parse text styling
        $fontSize = (int) ($style['font-size'] ?? 16);
        $color = $style['color'] ?? '#000000';
        $fontWeight = $style['font-weight'] ?? 'normal';
        
        // Convert color to RGB
        $rgb = $this->hexToRgb($color);
        
        try {
            // Scale coordinates for higher resolution
            $scaledX = $x * 2;
            $scaledY = $y * 2;
            $scaledFontSize = $fontSize * 2;

            // For now, use a simple text overlay
            // In a production environment, you would use a proper font file
            $image->text($text, $scaledX, $scaledY, function ($font) use ($scaledFontSize, $rgb, $fontWeight) {
                $font->size($scaledFontSize);
                $font->color($rgb['r'], $rgb['g'], $rgb['b']);
                // Note: Font file would be specified here in production
                // $font->file(storage_path('fonts/arial.ttf'));
            });
        } catch (\Exception $e) {
            \Log::warning('Failed to add text element: ' . $e->getMessage());
        }
    }
    
    /**
     * Extract text content from HTML and replace with business data.
     */
    private function extractTextFromHtml(string $innerHTML, string $type, ?Business $business): string
    {
        // Remove HTML tags
        $text = strip_tags($innerHTML);
        
        // Replace business placeholders with actual data
        if ($business) {
            switch ($type) {
                case 'business-name':
                    return $business->name ?: 'Business Name';
                case 'business-slogan':
                    return $business->slogan ?: 'Your Slogan Here';
                case 'business-phone':
                    return $business->phone ?: '+****************';
                case 'business-email':
                    return $business->email ?: '<EMAIL>';
                case 'business-website':
                    return $business->website ?: 'www.business.com';
                case 'business-address':
                    return $business->address ?: 'Business Address';
            }
        }
        
        return $text;
    }
    
    /**
     * Parse CSS style string into array.
     */
    private function parseStyle(string $styleString): array
    {
        $styles = [];
        $declarations = explode(';', $styleString);
        
        foreach ($declarations as $declaration) {
            $parts = explode(':', $declaration, 2);
            if (count($parts) === 2) {
                $property = trim($parts[0]);
                $value = trim($parts[1]);
                
                // Convert pixel values to integers
                if (str_ends_with($value, 'px')) {
                    $value = (int) str_replace('px', '', $value);
                }
                
                $styles[$property] = $value;
            }
        }
        
        return $styles;
    }
    
    /**
     * Convert hex color to RGB array.
     */
    private function hexToRgb(string $hex): array
    {
        $hex = ltrim($hex, '#');
        
        if (strlen($hex) === 3) {
            $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
        }
        
        return [
            'r' => hexdec(substr($hex, 0, 2)),
            'g' => hexdec(substr($hex, 2, 2)),
            'b' => hexdec(substr($hex, 4, 2)),
        ];
    }
    
    /**
     * Generate unique filename for download.
     */
    private function generateFilename(Download $download): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $random = Str::random(8);
        $extension = $download->file_format;
        
        return "design_{$download->user_id}_{$timestamp}_{$random}.{$extension}";
    }
}
