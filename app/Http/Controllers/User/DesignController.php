<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Poster;
use App\Models\Frame;
use App\Models\Business;
use App\Models\Download;
use App\Models\Setting;
use App\Services\ImageGenerationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class DesignController extends Controller
{
    /**
     * Show the design editor.
     */
    public function create(Request $request)
    {
        $type = $request->get('type', 'poster');
        $id = $request->get('id');
        
        if (!in_array($type, ['poster', 'frame'])) {
            abort(404);
        }
        
        // Get the template
        $template = null;
        if ($type === 'poster' && $id) {
            $template = Poster::where('status', true)->findOrFail($id);
        } elseif ($type === 'frame' && $id) {
            $template = Frame::where('status', true)->findOrFail($id);
        }
        
        // Get user's businesses
        $businesses = Auth::user()->businesses()
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();
        
        // Get default business
        $defaultBusiness = $businesses->where('is_default', true)->first() ?? $businesses->first();
        
        // Get available frames for the editor
        $frames = Frame::where('status', true)
            ->orderBy('created_at', 'desc')
            ->take(20)
            ->get()
            ->map(function ($frame) {
                return [
                    'id' => $frame->id,
                    'name' => $frame->name,
                    'image_path' => $frame->image_path,
                    'image_url' => $frame->image_path ? Storage::url($frame->image_path) : null,
                    'is_premium' => $frame->is_premium,
                ];
            });
        
        return view('user.design.editor', compact('template', 'type', 'businesses', 'defaultBusiness', 'frames'));
    }
    
    /**
     * Save design as draft.
     */
    public function save(Request $request)
    {
        $request->validate([
            'design_data' => 'required|json',
            'template_id' => 'nullable|integer',
            'template_type' => 'required|in:poster,frame',
            'business_id' => 'nullable|exists:businesses,id',
        ]);
        
        // For now, just return success
        // In a full implementation, you would save the design to a designs table
        return response()->json([
            'status' => 'success',
            'message' => 'Design saved successfully',
            'design_id' => rand(1000, 9999) // Placeholder
        ]);
    }
    
    /**
     * Generate and download the final image.
     */
    public function download(Request $request, ImageGenerationService $imageService)
    {
        $request->validate([
            'design_data' => 'required|json',
            'template_id' => 'nullable|integer',
            'template_type' => 'required|in:poster,frame',
            'business_id' => 'nullable|exists:businesses,id',
            'format' => 'in:png,jpg,pdf',
            'quality' => 'integer|min:1|max:100',
        ]);

        $user = Auth::user();
        $designData = json_decode($request->design_data, true);

        // Determine download type
        $downloadType = $this->determineDownloadType($user);

        // Create download record
        $download = Download::create([
            'user_id' => $user->id,
            'business_id' => $request->business_id,
            'template_type' => $request->template_type,
            'template_id' => $request->template_id,
            'design_data' => $designData,
            'file_name' => 'design_' . time(),
            'file_format' => $request->format ?? 'png',
            'download_type' => $downloadType,
            'cost' => $downloadType === 'paid' ? (float) Setting::get('per_download_cost', '1.00') : 0.00,
            'status' => 'pending',
        ]);

        // Generate image
        $success = $imageService->generateImage($download);

        if ($success) {
            return response()->json([
                'status' => 'success',
                'message' => 'Image generated successfully!',
                'download_url' => $download->file_url,
                'download_id' => $download->id,
                'file_size' => $download->formatted_file_size
            ]);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to generate image: ' . $download->error_message
            ], 500);
        }
    }

    /**
     * Determine the download type for the user.
     */
    private function determineDownloadType($user): string
    {
        // Check if user has active subscription
        $hasActiveSubscription = $user->subscriptions()
            ->where('status', 'active')
            ->where('end_date', '>', now())
            ->exists();

        if ($hasActiveSubscription) {
            return 'subscription';
        }

        // Check if free downloads are enabled
        if (Setting::get('free_download_enabled', 'true') === 'true') {
            return 'free';
        }

        // Default to paid
        return 'paid';
    }
    
    /**
     * Get business details for the editor.
     */
    public function getBusiness(Request $request, Business $business)
    {
        // Ensure user can only access their own businesses
        if ($business->user_id !== Auth::id()) {
            abort(404);
        }
        
        return response()->json([
            'status' => 'success',
            'data' => [
                'id' => $business->id,
                'name' => $business->name,
                'slogan' => $business->slogan,
                'address' => $business->address,
                'phone' => $business->phone,
                'whatsapp' => $business->whatsapp,
                'email' => $business->email,
                'website' => $business->website,
                'logo_url' => $business->logo_url,
                'is_default' => $business->is_default,
            ]
        ]);
    }
}
