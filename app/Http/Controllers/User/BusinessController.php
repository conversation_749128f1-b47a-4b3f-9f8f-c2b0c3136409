<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Business;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class BusinessController extends Controller
{
    /**
     * Display a listing of the user's businesses.
     */
    public function index()
    {
        $businesses = Auth::user()->businesses()
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('user.businesses.index', compact('businesses'));
    }

    /**
     * Store a newly created business.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slogan' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:1000',
            'phone' => 'nullable|string|max:20',
            'whatsapp' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'is_default' => 'boolean',
        ]);

        $data = $request->only([
            'name', 'slogan', 'address', 'phone', 
            'whatsapp', 'email', 'website'
        ]);
        
        $data['user_id'] = Auth::id();
        $data['is_default'] = $request->boolean('is_default');

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('business-logos', 'public');
            $data['logo_path'] = $logoPath;
        }

        // If this is the user's first business, make it default
        if (Auth::user()->businesses()->count() === 0) {
            $data['is_default'] = true;
        }

        $business = Business::create($data);

        if ($request->ajax()) {
            return response()->json([
                'status' => 'success',
                'message' => 'Business profile created successfully!',
                'business' => $business->load('user')
            ]);
        }

        return redirect()->back()->with('success', 'Business profile created successfully!');
    }

    /**
     * Show the form for editing the specified business.
     */
    public function edit(Business $business)
    {
        // Ensure user can only edit their own businesses
        if ($business->user_id !== Auth::id()) {
            abort(404);
        }

        if (request()->ajax()) {
            return response()->json([
                'status' => 'success',
                'business' => $business
            ]);
        }

        return view('user.businesses.edit', compact('business'));
    }

    /**
     * Update the specified business.
     */
    public function update(Request $request, Business $business)
    {
        // Ensure user can only update their own businesses
        if ($business->user_id !== Auth::id()) {
            abort(404);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'slogan' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:1000',
            'phone' => 'nullable|string|max:20',
            'whatsapp' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'is_default' => 'boolean',
        ]);

        $data = $request->only([
            'name', 'slogan', 'address', 'phone', 
            'whatsapp', 'email', 'website'
        ]);
        
        $data['is_default'] = $request->boolean('is_default');

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($business->logo_path) {
                Storage::disk('public')->delete($business->logo_path);
            }
            
            $logoPath = $request->file('logo')->store('business-logos', 'public');
            $data['logo_path'] = $logoPath;
        }

        $business->update($data);

        if ($request->ajax()) {
            return response()->json([
                'status' => 'success',
                'message' => 'Business profile updated successfully!',
                'business' => $business->fresh()
            ]);
        }

        return redirect()->back()->with('success', 'Business profile updated successfully!');
    }

    /**
     * Remove the specified business.
     */
    public function destroy(Business $business)
    {
        // Ensure user can only delete their own businesses
        if ($business->user_id !== Auth::id()) {
            abort(404);
        }

        // Prevent deletion if it's the only business
        if (Auth::user()->businesses()->count() === 1) {
            if (request()->ajax()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Cannot delete your only business profile.'
                ], 422);
            }
            
            return redirect()->back()->with('error', 'Cannot delete your only business profile.');
        }

        // If deleting default business, make another one default
        if ($business->is_default) {
            $nextBusiness = Auth::user()->businesses()
                ->where('id', '!=', $business->id)
                ->first();
            
            if ($nextBusiness) {
                $nextBusiness->update(['is_default' => true]);
            }
        }

        $business->delete();

        if (request()->ajax()) {
            return response()->json([
                'status' => 'success',
                'message' => 'Business profile deleted successfully!'
            ]);
        }

        return redirect()->back()->with('success', 'Business profile deleted successfully!');
    }

    /**
     * Set a business as default.
     */
    public function setDefault(Business $business)
    {
        // Ensure user can only modify their own businesses
        if ($business->user_id !== Auth::id()) {
            abort(404);
        }

        $business->update(['is_default' => true]);

        if (request()->ajax()) {
            return response()->json([
                'status' => 'success',
                'message' => 'Default business updated successfully!',
                'business' => $business->fresh()
            ]);
        }

        return redirect()->back()->with('success', 'Default business updated successfully!');
    }
}
