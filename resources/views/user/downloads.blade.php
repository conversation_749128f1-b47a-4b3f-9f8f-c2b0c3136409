@extends('layouts.user')

@section('title', 'Download History - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', 'View and manage your download history')

@push('styles')
<style>
    .downloads-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }

    .downloads-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="download-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><path d="M15 5 L15 20 M10 15 L15 20 L20 15" stroke="rgba(255,255,255,0.1)" stroke-width="2" fill="none"/></pattern></defs><rect width="100" height="100" fill="url(%23download-pattern)"/></svg>');
        opacity: 0.3;
    }

    .downloads-header .container {
        position: relative;
        z-index: 1;
    }

    .downloads-header h1 {
        color: white;
        margin-bottom: 0.5rem;
        font-size: 2.5rem;
        font-weight: 700;
    }

    .downloads-header p {
        font-size: 1.125rem;
        opacity: 0.9;
    }
    
    .downloads-container {
        max-width: 1000px;
        margin: 0 auto;
    }
    
    .downloads-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #64748b;
        font-size: 0.875rem;
    }
    
    .downloads-list {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .downloads-header-section {
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
        background: #f8fafc;
    }
    
    .downloads-header-section h3 {
        margin: 0;
        color: #1e293b;
    }
    
    .download-item {
        display: grid;
        grid-template-columns: auto 1fr auto auto auto;
        gap: 1rem;
        padding: 1.5rem;
        border-bottom: 1px solid #f1f5f9;
        align-items: center;
        transition: background-color 0.2s ease;
    }
    
    .download-item:hover {
        background: #f8fafc;
    }
    
    .download-item:last-child {
        border-bottom: none;
    }
    
    .download-preview {
        width: 60px;
        height: 60px;
        background: #f1f5f9;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
        position: relative;
        overflow: hidden;
    }
    
    .download-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .download-info {
        flex: 1;
    }
    
    .download-name {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.25rem;
    }
    
    .download-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        font-size: 0.875rem;
        color: #64748b;
    }
    
    .download-meta span {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }
    
    .download-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
    }
    
    .status-completed {
        background: #dcfce7;
        color: #166534;
    }
    
    .status-processing {
        background: #fef3c7;
        color: #92400e;
    }
    
    .status-failed {
        background: #fef2f2;
        color: #dc2626;
    }
    
    .status-pending {
        background: #e0e7ff;
        color: #3730a3;
    }
    
    .download-type {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .type-free {
        background: #dcfce7;
        color: #166534;
    }
    
    .type-subscription {
        background: #fef3c7;
        color: #92400e;
    }
    
    .type-paid {
        background: #dbeafe;
        color: #1d4ed8;
    }
    
    .download-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .action-btn {
        padding: 6px 12px;
        border: 1px solid #e5e7eb;
        background: white;
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        text-decoration: none;
        color: #374151;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }
    
    .action-btn:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
    }
    
    .action-btn.primary {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }
    
    .action-btn.primary:hover {
        background: color-mix(in srgb, var(--primary-color) 85%, black);
    }
    
    .action-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #64748b;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #cbd5e1;
    }
    
    .pagination-wrapper {
        padding: 1.5rem;
        border-top: 1px solid #e5e7eb;
        background: #f8fafc;
    }
    
    @media (max-width: 768px) {
        .download-item {
            grid-template-columns: auto 1fr;
            gap: 1rem;
        }
        
        .download-status,
        .download-type,
        .download-actions {
            grid-column: 1 / -1;
            justify-self: start;
            margin-top: 0.5rem;
        }
        
        .download-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .downloads-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
@endpush

@section('content')
<!-- Downloads Header -->
<section class="downloads-header">
    <div class="container">
        <h1>Download History</h1>
        <p>View and manage all your downloaded designs</p>
    </div>
</section>

<div class="container">
    <div class="downloads-container">
        <!-- Download Stats -->
        <div class="downloads-stats">
            <div class="stat-card">
                <div class="stat-value">{{ $downloads->total() }}</div>
                <div class="stat-label">Total Downloads</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value">{{ $downloads->where('status', 'completed')->count() }}</div>
                <div class="stat-label">Completed</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value">{{ $downloads->where('download_type', 'free')->count() }}</div>
                <div class="stat-label">Free Downloads</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value">₹{{ number_format($downloads->sum('cost'), 2) }}</div>
                <div class="stat-label">Total Spent</div>
            </div>
        </div>

        <!-- Downloads List -->
        <div class="downloads-list">
            <div class="downloads-header-section">
                <h3>Your Downloads</h3>
            </div>
            
            @if($downloads->count() > 0)
                @foreach($downloads as $download)
                    <div class="download-item">
                        <div class="download-preview">
                            @if($download->file_url && $download->status === 'completed')
                                <img src="{{ $download->file_url }}" alt="Download preview">
                            @else
                                <i class="fas fa-image fa-lg"></i>
                            @endif
                        </div>
                        
                        <div class="download-info">
                            <div class="download-name">{{ $download->file_name }}</div>
                            <div class="download-meta">
                                <span>
                                    <i class="fas fa-calendar"></i>
                                    {{ $download->created_at->format('M j, Y') }}
                                </span>
                                @if($download->business)
                                    <span>
                                        <i class="fas fa-building"></i>
                                        {{ $download->business->name }}
                                    </span>
                                @endif
                                @if($download->file_size)
                                    <span>
                                        <i class="fas fa-file"></i>
                                        {{ $download->formatted_file_size }}
                                    </span>
                                @endif
                                <span>
                                    <i class="fas fa-image"></i>
                                    {{ strtoupper($download->file_format) }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="download-status status-{{ $download->status }}">
                            {{ ucfirst($download->status) }}
                        </div>
                        
                        <div class="download-type type-{{ $download->download_type }}">
                            {{ ucfirst($download->download_type) }}
                            @if($download->cost > 0)
                                (₹{{ number_format($download->cost, 2) }})
                            @endif
                        </div>
                        
                        <div class="download-actions">
                            @if($download->status === 'completed' && $download->file_path)
                                <a href="{{ route('user.downloads.file', $download) }}" class="action-btn primary">
                                    <i class="fas fa-download"></i>
                                    Download
                                </a>
                            @elseif($download->status === 'failed')
                                <button class="action-btn" disabled>
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Failed
                                </button>
                            @elseif($download->status === 'processing')
                                <button class="action-btn" disabled>
                                    <i class="fas fa-spinner fa-spin"></i>
                                    Processing
                                </button>
                            @else
                                <button class="action-btn" disabled>
                                    <i class="fas fa-clock"></i>
                                    Pending
                                </button>
                            @endif
                        </div>
                    </div>
                @endforeach
                
                @if($downloads->hasPages())
                    <div class="pagination-wrapper">
                        {{ $downloads->links() }}
                    </div>
                @endif
            @else
                <div class="empty-state">
                    <i class="fas fa-download"></i>
                    <h3>No Downloads Yet</h3>
                    <p>Start creating designs to see your download history here</p>
                    <a href="{{ route('user.categories') }}" class="btn btn-primary" style="margin-top: 1rem;">
                        <i class="fas fa-plus"></i> Create Your First Design
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
