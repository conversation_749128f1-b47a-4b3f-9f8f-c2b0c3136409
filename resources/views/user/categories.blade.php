@extends('layouts.user')

@section('title', 'Categories - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', 'Browse our extensive collection of design categories and templates')

@push('styles')
<style>
    .page-header {
        text-align: center;
        padding: 4rem 0;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        margin-bottom: 4rem;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
        animation: float 20s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .page-header .container {
        position: relative;
        z-index: 1;
    }

    .page-header h1 {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: white;
        font-weight: 800;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .page-header p {
        font-size: 1.25rem;
        opacity: 0.95;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
    }
    
    .categories-container {
        margin-bottom: 3rem;
    }
    
    .category-section {
        margin-bottom: 4rem;
    }
    
    .category-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #e5e7eb;
    }

    .category-stats {
        margin-bottom: 1rem;
        text-align: center;
    }

    .template-count {
        background: var(--primary-color);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
        display: inline-block;
    }
    
    .category-icon {
        font-size: 2rem;
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        transition: all 0.3s ease;
        position: relative;
    }

    .category-icon::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: 20px;
        padding: 2px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
    }
    
    .category-info h2 {
        margin-bottom: 0.5rem;
        color: #1e293b;
    }
    
    .category-info p {
        color: #64748b;
        margin: 0;
    }
    
    .subcategories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .subcategory-card {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        text-decoration: none;
        color: inherit;
        border: 2px solid #f1f5f9;
        position: relative;
        overflow: hidden;
    }

    .subcategory-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: left 0.5s;
    }

    .subcategory-card:hover::before {
        left: 100%;
    }

    .subcategory-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        border-color: var(--primary-color);
    }
    
    .subcategory-header {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .subcategory-header h3 {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
    }

    .subcategory-count {
        background: #f3f4f6;
        color: #6b7280;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        align-self: flex-start;
    }
    
    .subcategory-icon {
        font-size: 1.5rem;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.25);
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .subcategory-card:hover .subcategory-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    }
    
    .subcategory-card h3 {
        margin: 0;
        font-size: 1.125rem;
        color: #1e293b;
    }
    
    .subcategory-card p {
        color: #64748b;
        font-size: 0.9rem;
        margin: 0;
        line-height: 1.4;
    }
    
    .view-all-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
        color: var(--primary-color);
        font-weight: 500;
        font-size: 0.9rem;
    }
    
    .single-category-card {
        background: white;
        border-radius: 20px;
        padding: 3rem 2rem;
        box-shadow: 0 4px 25px rgba(0,0,0,0.08);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        text-decoration: none;
        color: inherit;
        text-align: center;
        border: 2px solid #f1f5f9;
        position: relative;
        overflow: hidden;
    }

    .single-category-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: left 0.5s;
    }

    .single-category-card:hover::before {
        left: 100%;
    }

    .single-category-card:hover {
        transform: translateY(-10px) scale(1.03);
        box-shadow: 0 15px 50px rgba(0,0,0,0.15);
        border-color: var(--primary-color);
    }
    
    .single-category-icon {
        font-size: 4rem;
        margin-bottom: 1.5rem;
        display: inline-block;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        transition: all 0.3s ease;
    }

    .single-category-card:hover .single-category-icon {
        transform: scale(1.1) rotate(-5deg);
    }
    
    .single-category-card h3 {
        margin-bottom: 0.5rem;
        color: #1e293b;
    }

    .category-template-count {
        background: var(--primary-color);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .single-category-card p {
        color: #64748b;
        font-size: 0.9rem;
    }
    
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #64748b;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #cbd5e1;
    }
    
    .empty-state h3 {
        margin-bottom: 1rem;
        color: #475569;
    }
    
    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }
        
        .category-header {
            flex-direction: column;
            text-align: center;
        }
        
        .subcategories-grid {
            grid-template-columns: 1fr;
        }
        
        .single-category-card {
            padding: 1.5rem;
        }
    }
</style>
@endpush

@section('content')
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1>Design Categories</h1>
        <p>Explore our collection of professionally designed templates</p>
    </div>
</section>

<!-- Categories Content -->
<div class="container">
    <div class="categories-container">
        @if($categories->count() > 0)
            @foreach($categories as $category)
                <div class="category-section">
                    <div class="category-header">
                        <div class="category-icon">
                            @if($category->icon)
                                {{ $category->icon }}
                            @else
                                <i class="fas fa-folder"></i>
                            @endif
                        </div>
                        <div class="category-info">
                            <h2>{{ $category->name }}</h2>
                            @if($category->description)
                                <p>{{ $category->description }}</p>
                            @endif
                        </div>
                    </div>
                    
                    @if($category->children->count() > 0)
                        <!-- Category with subcategories -->
                        <div class="category-stats">
                            <span class="template-count">{{ $category->template_count }} templates total</span>
                        </div>
                        <div class="subcategories-grid">
                            @foreach($category->children as $subcategory)
                                <a href="{{ route('user.categories.show', $subcategory) }}" class="subcategory-card">
                                    <div class="subcategory-icon">
                                        @if($subcategory->icon)
                                            {{ $subcategory->icon }}
                                        @else
                                            <i class="fas fa-image"></i>
                                        @endif
                                    </div>
                                    <div class="subcategory-header">
                                        <h3>{{ $subcategory->name }}</h3>
                                        <span class="subcategory-count">{{ $subcategory->template_count }} templates</span>
                                    </div>
                                    @if($subcategory->description)
                                        <p>{{ Str::limit($subcategory->description, 100) }}</p>
                                    @endif
                                    <div class="view-all-btn">
                                        <span>View Templates</span>
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                </a>
                            @endforeach
                        </div>
                    @else
                        <!-- Single category without subcategories -->
                        <div class="subcategories-grid">
                            <a href="{{ route('user.categories.show', $category) }}" class="single-category-card">
                                @if($category->icon)
                                    <span class="single-category-icon">{{ $category->icon }}</span>
                                @else
                                    <span class="single-category-icon">🎨</span>
                                @endif
                                <h3>{{ $category->name }}</h3>
                                <span class="category-template-count">{{ $category->template_count }} templates</span>
                                @if($category->description)
                                    <p>{{ Str::limit($category->description, 120) }}</p>
                                @endif
                                <div class="view-all-btn">
                                    <span>View Templates</span>
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                            </a>
                        </div>
                    @endif
                </div>
            @endforeach
        @else
            <!-- Empty State -->
            <div class="empty-state">
                <i class="fas fa-folder-open"></i>
                <h3>No Categories Available</h3>
                <p>Categories are being added. Please check back later.</p>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Add smooth scrolling for better UX
    document.addEventListener('DOMContentLoaded', function() {
        // Add loading states to category links
        const categoryLinks = document.querySelectorAll('.subcategory-card, .single-category-card');
        
        categoryLinks.forEach(link => {
            link.addEventListener('click', function() {
                // Add a subtle loading indicator
                const btn = this.querySelector('.view-all-btn');
                if (btn) {
                    const originalText = btn.innerHTML;
                    btn.innerHTML = '<span class="spinner"></span> Loading...';
                    
                    // Reset after a delay if navigation fails
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                    }, 3000);
                }
            });
        });
    });
</script>
@endpush
