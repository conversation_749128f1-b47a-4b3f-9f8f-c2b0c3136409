@extends('layouts.user')

@section('title', 'Design Editor - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', 'Create and customize your social media posts with our advanced design editor')

@push('styles')
<style>
    body {
        overflow: hidden;
    }
    
    .editor-container {
        height: 100vh;
        display: flex;
        flex-direction: column;
    }
    
    .editor-header {
        background: white;
        border-bottom: 1px solid #e5e7eb;
        padding: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        z-index: 100;
    }
    
    .editor-title {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .editor-title h1 {
        font-size: 1.25rem;
        margin: 0;
        color: #1e293b;
    }
    
    .editor-actions {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .editor-main {
        flex: 1;
        display: flex;
        overflow: hidden;
    }
    
    .sidebar {
        width: 300px;
        background: #f8fafc;
        border-right: 1px solid #e5e7eb;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
    }
    
    .sidebar-tabs {
        display: flex;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .sidebar-tab {
        flex: 1;
        padding: 1rem;
        background: none;
        border: none;
        cursor: pointer;
        font-weight: 500;
        color: #64748b;
        border-bottom: 3px solid transparent;
        transition: all 0.2s ease;
    }
    
    .sidebar-tab.active {
        color: var(--primary-color);
        border-bottom-color: var(--primary-color);
        background: white;
    }
    
    .sidebar-content {
        flex: 1;
        padding: 1rem;
    }
    
    .tab-panel {
        display: none;
    }
    
    .tab-panel.active {
        display: block;
    }
    
    .canvas-container {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f1f5f9;
        position: relative;
        overflow: hidden;
    }
    
    .canvas-wrapper {
        position: relative;
        background: white;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        border-radius: 8px;
        overflow: hidden;
    }
    
    .design-canvas {
        width: 400px;
        height: 400px;
        position: relative;
        background: white;
        cursor: crosshair;
        user-select: none;
    }
    
    .canvas-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }
    
    .design-element {
        position: absolute;
        cursor: move;
        user-select: none;
        z-index: 10;
        border: 2px solid transparent;
        transition: border-color 0.2s ease;
    }
    
    .design-element:hover {
        border-color: var(--primary-color);
    }
    
    .design-element.selected {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 1px var(--primary-color);
    }
    
    .design-element.dragging {
        z-index: 100;
        transform: rotate(5deg);
    }
    
    .element-text {
        padding: 8px;
        background: rgba(255,255,255,0.9);
        border-radius: 4px;
        font-family: var(--body-font);
        color: #1e293b;
        white-space: nowrap;
        min-width: 100px;
        text-align: center;
    }
    
    .element-logo {
        width: 80px;
        height: 80px;
        object-fit: contain;
        background: rgba(255,255,255,0.9);
        border-radius: 8px;
        padding: 4px;
    }
    
    .resize-handle {
        position: absolute;
        width: 8px;
        height: 8px;
        background: var(--primary-color);
        border: 1px solid white;
        border-radius: 50%;
        cursor: nw-resize;
    }
    
    .resize-handle.se {
        bottom: -4px;
        right: -4px;
    }
    
    .business-selector {
        margin-bottom: 1rem;
    }
    
    .business-select {
        width: 100%;
        padding: 8px 12px;
        border: 2px solid #e5e7eb;
        border-radius: 6px;
        background: white;
    }
    
    .element-list {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .element-item {
        padding: 0.75rem;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .element-item:hover {
        border-color: var(--primary-color);
        background: #f0f9ff;
    }
    
    .element-icon {
        width: 20px;
        text-align: center;
        color: var(--primary-color);
    }
    
    .frames-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    .frame-item {
        aspect-ratio: 1;
        background: white;
        border: 2px solid #e5e7eb;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
    }
    
    .frame-item:hover {
        border-color: var(--primary-color);
    }
    
    .frame-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
    }
    
    .properties-panel {
        background: white;
        border-radius: 6px;
        padding: 1rem;
        margin-top: 1rem;
        border: 1px solid #e5e7eb;
    }
    
    .property-group {
        margin-bottom: 1rem;
    }
    
    .property-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
        font-size: 0.875rem;
    }
    
    .property-input {
        width: 100%;
        padding: 6px 8px;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
        font-size: 14px;
    }
    
    .property-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
    }
    
    .color-input {
        width: 40px;
        height: 32px;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .zoom-controls {
        position: absolute;
        bottom: 1rem;
        right: 1rem;
        display: flex;
        gap: 0.5rem;
        background: white;
        border-radius: 6px;
        padding: 0.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .zoom-btn {
        width: 32px;
        height: 32px;
        border: 1px solid #e5e7eb;
        background: white;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
    }
    
    .zoom-btn:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
    }
    
    .undo-redo {
        display: flex;
        gap: 0.5rem;
    }
    
    .history-btn {
        padding: 6px 12px;
        border: 1px solid #e5e7eb;
        background: white;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s ease;
    }
    
    .history-btn:hover:not(:disabled) {
        border-color: var(--primary-color);
        color: var(--primary-color);
    }
    
    .history-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    @media (max-width: 768px) {
        .editor-main {
            flex-direction: column;
        }
        
        .sidebar {
            width: 100%;
            height: 200px;
        }
        
        .design-canvas {
            width: 300px;
            height: 300px;
        }
        
        .editor-actions {
            flex-direction: column;
            gap: 0.5rem;
        }
    }
</style>
@endpush

@section('content')
<div class="editor-container">
    <!-- Editor Header -->
    <div class="editor-header">
        <div class="editor-title">
            <a href="{{ url()->previous() }}" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Back
            </a>
            <h1>Design Editor</h1>
            @if($template)
                <span class="badge">{{ ucfirst($type) }}: {{ $template->name }}</span>
            @endif
        </div>
        
        <div class="editor-actions">
            <div class="undo-redo">
                <button class="history-btn" id="undoBtn" disabled>
                    <i class="fas fa-undo"></i> Undo
                </button>
                <button class="history-btn" id="redoBtn" disabled>
                    <i class="fas fa-redo"></i> Redo
                </button>
            </div>
            
            <button class="btn btn-outline" onclick="saveDesign()">
                <i class="fas fa-save"></i> Save
            </button>
            
            <button class="btn btn-primary" onclick="downloadDesign()">
                <i class="fas fa-download"></i> Download
            </button>
        </div>
    </div>
    
    <!-- Editor Main -->
    <div class="editor-main">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-tabs">
                <button class="sidebar-tab active" data-tab="elements">Elements</button>
                <button class="sidebar-tab" data-tab="frames">Frames</button>
                <button class="sidebar-tab" data-tab="properties">Properties</button>
            </div>
            
            <div class="sidebar-content">
                <!-- Elements Tab -->
                <div id="elements" class="tab-panel active">
                    <div class="business-selector">
                        <label class="property-label">Business Profile</label>
                        <select class="business-select" id="businessSelect">
                            @foreach($businesses as $business)
                                <option value="{{ $business->id }}" {{ $business->is_default ? 'selected' : '' }}>
                                    {{ $business->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="element-list">
                        <div class="element-item" onclick="addElement('business-name')">
                            <i class="fas fa-building element-icon"></i>
                            <span>Business Name</span>
                        </div>
                        
                        <div class="element-item" onclick="addElement('business-slogan')">
                            <i class="fas fa-quote-left element-icon"></i>
                            <span>Business Slogan</span>
                        </div>
                        
                        <div class="element-item" onclick="addElement('business-phone')">
                            <i class="fas fa-phone element-icon"></i>
                            <span>Phone Number</span>
                        </div>
                        
                        <div class="element-item" onclick="addElement('business-email')">
                            <i class="fas fa-envelope element-icon"></i>
                            <span>Email Address</span>
                        </div>
                        
                        <div class="element-item" onclick="addElement('business-website')">
                            <i class="fas fa-globe element-icon"></i>
                            <span>Website</span>
                        </div>
                        
                        <div class="element-item" onclick="addElement('business-address')">
                            <i class="fas fa-map-marker-alt element-icon"></i>
                            <span>Address</span>
                        </div>
                        
                        <div class="element-item" onclick="addElement('business-logo')">
                            <i class="fas fa-image element-icon"></i>
                            <span>Business Logo</span>
                        </div>
                        
                        <div class="element-item" onclick="addElement('custom-text')">
                            <i class="fas fa-font element-icon"></i>
                            <span>Custom Text</span>
                        </div>
                    </div>
                </div>
                
                <!-- Frames Tab -->
                <div id="frames" class="tab-panel">
                    <div class="frames-grid">
                        <div class="frame-item" onclick="changeFrame(null)">
                            <i class="fas fa-times"></i>
                        </div>
                        @foreach($frames as $frame)
                            <div class="frame-item" onclick="changeFrame({{ $frame['id'] }})">
                                @if($frame['image_path'])
                                    <img src="{{ $frame['image_url'] }}" alt="{{ $frame['name'] }}">
                                @else
                                    <i class="fas fa-border-style"></i>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
                
                <!-- Properties Tab -->
                <div id="properties" class="tab-panel">
                    <div id="propertiesContent">
                        <p style="color: #64748b; text-align: center; padding: 2rem;">
                            Select an element to edit its properties
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Canvas -->
        <div class="canvas-container">
            <div class="canvas-wrapper">
                <div class="design-canvas" id="designCanvas">
                    @if($template && $template->image_path)
                        <img src="{{ Storage::url($template->image_path) }}" alt="{{ $template->name }}" class="canvas-background">
                    @endif
                </div>
            </div>
            
            <!-- Zoom Controls -->
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="zoomOut()">
                    <i class="fas fa-minus"></i>
                </button>
                <span id="zoomLevel">100%</span>
                <button class="zoom-btn" onclick="zoomIn()">
                    <i class="fas fa-plus"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden data -->
<script>
    window.editorData = {
        template: @json($template),
        type: '{{ $type }}',
        businesses: @json($businesses),
        defaultBusiness: @json($defaultBusiness),
        frames: @json($frames),
        csrfToken: '{{ csrf_token() }}'
    };
</script>
@endsection

@push('scripts')
<script src="{{ asset('js/design-editor.js') }}"></script>
@endpush
