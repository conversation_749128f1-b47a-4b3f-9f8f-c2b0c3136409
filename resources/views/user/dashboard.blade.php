@extends('layouts.user')

@section('title', 'Account Dashboard - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', 'Manage your account, profile, businesses, and design projects')

@push('styles')
<style>
    .dashboard-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }
    
    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }
    
    .dashboard-header .container {
        position: relative;
        z-index: 1;
    }
    
    .dashboard-header h1 {
        color: white;
        margin-bottom: 0.5rem;
        font-size: 2.5rem;
        font-weight: 700;
    }
    
    .dashboard-header p {
        opacity: 0.9;
        font-size: 1.125rem;
    }
    
    .welcome-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .quick-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .quick-action-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }
    
    .quick-action-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.06);
        border: 1px solid #f1f5f9;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        text-align: center;
    }
    
    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
    }
    
    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        margin: 0 auto 1rem;
    }
    
    .stat-value {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.25rem;
        line-height: 1;
    }
    
    .stat-label {
        color: #64748b;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    /* Tabbed Interface Styles */
    .dashboard-tabs {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #f1f5f9;
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .tab-nav {
        display: flex;
        background: #f8fafc;
        border-bottom: 1px solid #f1f5f9;
        overflow-x: auto;
    }
    
    .tab-nav::-webkit-scrollbar {
        height: 2px;
    }
    
    .tab-nav::-webkit-scrollbar-track {
        background: #f1f5f9;
    }
    
    .tab-nav::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 1px;
    }
    
    .tab-button {
        flex: 1;
        min-width: 150px;
        padding: 1rem 1.5rem;
        background: none;
        border: none;
        color: #64748b;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        white-space: nowrap;
    }
    
    .tab-button:hover {
        color: var(--primary-color);
        background: rgba(59, 130, 246, 0.05);
    }
    
    .tab-button.active {
        color: var(--primary-color);
        background: white;
    }
    
    .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    }
    
    .tab-content {
        padding: 2rem;
    }
    
    .tab-panel {
        display: none;
    }
    
    .tab-panel.active {
        display: block;
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #f1f5f9;
    }
    
    .section-header h3 {
        margin: 0;
        color: #1e293b;
        font-size: 1.25rem;
        font-weight: 700;
    }
    
    @media (max-width: 768px) {
        .welcome-section {
            flex-direction: column;
            text-align: center;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .tab-nav {
            flex-direction: column;
        }
        
        .tab-button {
            min-width: auto;
        }
    }
</style>
@endpush

@section('content')
<!-- Dashboard Header -->
<section class="dashboard-header">
    <div class="container">
        <div class="welcome-section">
            <div class="welcome-info">
                <h1>Welcome back, {{ $user->name }}! 👋</h1>
                <p>Ready to create amazing designs? Let's get started with your projects.</p>
            </div>
            <div class="quick-actions">
                <a href="{{ route('user.categories') }}" class="quick-action-btn">
                    <i class="fas fa-plus"></i> Create Design
                </a>
                <a href="{{ route('user.payment.plans') }}" class="quick-action-btn">
                    <i class="fas fa-crown"></i> View Plans
                </a>
                <a href="{{ route('user.downloads') }}" class="quick-action-btn">
                    <i class="fas fa-download"></i> Downloads
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Stats Grid -->
<div class="container">
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-download"></i>
            </div>
            <div class="stat-value">{{ $stats['total_downloads'] }}</div>
            <div class="stat-label">Total Downloads</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="stat-value">{{ $stats['this_month_downloads'] }}</div>
            <div class="stat-label">This Month</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-wallet"></i>
            </div>
            <div class="stat-value">₹{{ number_format($stats['wallet_balance'], 2) }}</div>
            <div class="stat-label">Wallet Balance</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-building"></i>
            </div>
            <div class="stat-value">{{ $stats['total_businesses'] }}</div>
            <div class="stat-label">Business Profiles</div>
        </div>
    </div>

    <!-- Dashboard Tabs -->
    <div class="dashboard-tabs">
        <div class="tab-nav">
            <button class="tab-button active" onclick="switchTab('profile-settings')">
                <i class="fas fa-user-cog"></i> Profile Settings
            </button>
            <button class="tab-button" onclick="switchTab('business-profiles')">
                <i class="fas fa-building"></i> Business Profiles
            </button>
            <button class="tab-button" onclick="switchTab('subscription')">
                <i class="fas fa-crown"></i> Subscription
            </button>
            <button class="tab-button" onclick="switchTab('transactions')">
                <i class="fas fa-receipt"></i> Recent Transactions
            </button>
            <button class="tab-button" onclick="switchTab('account-actions')">
                <i class="fas fa-cog"></i> Account Actions
            </button>
        </div>

        <div class="tab-content">
            <!-- Profile Settings Tab -->
            <div id="profile-settings" class="tab-panel active">
                <div class="section-header">
                    <h3>Profile Settings</h3>
                </div>
                <div class="profile-form">
                    <div class="form-group">
                        <label for="name">Full Name</label>
                        <input type="text" id="name" value="{{ $user->name }}" readonly style="background: #f9fafb; cursor: not-allowed;">
                    </div>
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input type="email" id="email" value="{{ $user->email }}" readonly style="background: #f9fafb; cursor: not-allowed;">
                    </div>
                    <div class="form-group">
                        <label>Account Type</label>
                        <div class="account-type" style="padding: 0.75rem; background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px; color: #059669;">
                            <i class="fab fa-google"></i> Google Account
                        </div>
                    </div>
                    <p class="note" style="color: #64748b; font-size: 0.875rem; margin-top: 1rem;">
                        <i class="fas fa-info-circle"></i>
                        Profile information is managed through your Google account and cannot be changed here.
                    </p>
                </div>
            </div>

            <!-- Business Profiles Tab -->
            <div id="business-profiles" class="tab-panel">
                <div class="section-header">
                    <h3>Business Profiles</h3>
                    <button class="btn btn-primary" onclick="openBusinessModal()">
                        <i class="fas fa-plus"></i> Add Business
                    </button>
                </div>
                @if($businesses->count() > 0)
                    <div class="business-list" id="businessList">
                        @foreach($businesses as $business)
                            <div class="business-item {{ $business->is_default ? 'default' : '' }}" data-business-id="{{ $business->id }}">
                                <div class="business-logo">
                                    @if($business->logo_path)
                                        <img src="{{ Storage::url($business->logo_path) }}" alt="{{ $business->name }}" class="business-logo-img">
                                    @else
                                        <i class="fas fa-building"></i>
                                    @endif
                                </div>
                                <div class="business-info">
                                    <div class="business-name">
                                        {{ $business->name }}
                                        @if($business->is_default)
                                            <span class="default-badge">Default</span>
                                        @endif
                                    </div>
                                    <div class="business-meta">
                                        @if($business->email)
                                            <i class="fas fa-envelope"></i> {{ $business->email }}
                                        @elseif($business->phone)
                                            <i class="fas fa-phone"></i> {{ $business->phone }}
                                        @else
                                            <i class="fas fa-info-circle"></i> No contact info
                                        @endif
                                    </div>
                                </div>
                                <div class="business-actions">
                                    @if(!$business->is_default)
                                        <button class="btn btn-outline btn-sm" onclick="setDefaultBusiness({{ $business->id }})" title="Set as Default">
                                            <i class="fas fa-star"></i>
                                        </button>
                                    @endif
                                    <button class="btn btn-outline btn-sm" onclick="editBusiness({{ $business->id }})" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    @if($businesses->count() > 1)
                                        <button class="btn btn-outline btn-sm btn-danger" onclick="deleteBusiness({{ $business->id }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="empty-state" id="emptyState" style="text-align: center; padding: 3rem; color: #64748b;">
                        <i class="fas fa-building" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <h4>No Business Profiles</h4>
                        <p>Add your first business profile to get started</p>
                        <button class="btn btn-primary" onclick="openBusinessModal()">
                            <i class="fas fa-plus"></i> Add Business
                        </button>
                    </div>
                @endif
            </div>

            <!-- Subscription Tab -->
            <div id="subscription" class="tab-panel">
                <div class="section-header">
                    <h3>Subscription Status</h3>
                    <a href="{{ route('user.payment.plans') }}" class="btn btn-primary">
                        <i class="fas fa-crown"></i> View Plans
                    </a>
                </div>
                @if(isset($currentSubscription) && $currentSubscription)
                    <div class="subscription-card active" style="padding: 2rem; background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%); border: 1px solid #bbf7d0; border-radius: 12px;">
                        <div class="subscription-info">
                            <h4 style="color: #059669; margin-bottom: 0.5rem;">{{ $currentSubscription->plan->name }}</h4>
                            <p style="color: #065f46; margin-bottom: 1rem;">Active until {{ $currentSubscription->end_date->format('F j, Y') }}</p>
                            <div class="subscription-features" style="display: flex; gap: 1rem; flex-wrap: wrap;">
                                <span class="feature" style="color: #059669;"><i class="fas fa-check"></i> Unlimited Downloads</span>
                                <span class="feature" style="color: #059669;"><i class="fas fa-check"></i> Premium Templates</span>
                                <span class="feature" style="color: #059669;"><i class="fas fa-check"></i> Priority Support</span>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="subscription-card inactive" style="padding: 2rem; background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; text-align: center;">
                        <div class="subscription-info">
                            <h4 style="color: #64748b; margin-bottom: 0.5rem;">No Active Subscription</h4>
                            <p style="color: #64748b; margin-bottom: 1rem;">Upgrade to unlock premium features and unlimited downloads</p>
                            <a href="{{ route('user.payment.plans') }}" class="btn btn-primary">
                                <i class="fas fa-crown"></i> View Plans
                            </a>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Recent Transactions Tab -->
            <div id="transactions" class="tab-panel">
                <div class="section-header">
                    <h3>Recent Transactions</h3>
                </div>
                <div class="transactions-list" style="text-align: center; padding: 3rem; color: #64748b;">
                    <i class="fas fa-receipt" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <h4>Transaction History</h4>
                    <p>Your payment and transaction history will appear here once implemented.</p>
                </div>
            </div>

            <!-- Account Actions Tab -->
            <div id="account-actions" class="tab-panel">
                <div class="section-header">
                    <h3>Account Actions</h3>
                </div>
                <div class="account-actions" style="display: flex; flex-direction: column; gap: 1.5rem;">
                    <div class="action-item" style="display: flex; justify-content: space-between; align-items: center; padding: 1.5rem; border: 1px solid #e2e8f0; border-radius: 12px;">
                        <div class="action-info">
                            <h4 style="margin: 0 0 0.5rem 0; color: #1e293b;">Download Your Data</h4>
                            <p style="margin: 0; color: #64748b;">Export all your account data including designs and business profiles</p>
                        </div>
                        <button class="btn btn-outline" onclick="downloadAccountData()">
                            <i class="fas fa-download"></i> Export Data
                        </button>
                    </div>

                    <div class="action-item danger" style="display: flex; justify-content: space-between; align-items: center; padding: 1.5rem; border: 1px solid #fecaca; border-radius: 12px; background: #fef2f2;">
                        <div class="action-info">
                            <h4 style="margin: 0 0 0.5rem 0; color: #dc2626;">Delete Account</h4>
                            <p style="margin: 0; color: #991b1b;">Permanently delete your account and all associated data</p>
                        </div>
                        <button class="btn btn-danger" onclick="confirmDeleteAccount()">
                            <i class="fas fa-trash"></i> Delete Account
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Tab switching functionality
    function switchTab(tabId) {
        // Remove active class from all tabs and panels
        document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));

        // Add active class to clicked tab and corresponding panel
        event.target.classList.add('active');
        document.getElementById(tabId).classList.add('active');
    }

    // Business management functions
    function openBusinessModal(businessId = null) {
        alert('Business modal functionality will be implemented soon.');
    }

    function setDefaultBusiness(businessId) {
        if (confirm('Set this business as your default?')) {
            alert('Set default business functionality will be implemented soon.');
        }
    }

    function editBusiness(businessId) {
        openBusinessModal(businessId);
    }

    function deleteBusiness(businessId) {
        if (confirm('Are you sure you want to delete this business profile?')) {
            alert('Delete business functionality will be implemented soon.');
        }
    }

    function downloadAccountData() {
        if (confirm('This will generate and download a file containing all your account data. Continue?')) {
            // Create a simple data export
            const userData = {
                name: '{{ $user->name }}',
                email: '{{ $user->email }}',
                memberSince: '{{ $user->created_at->format('Y-m-d H:i:s') }}',
                totalBusinesses: {{ $stats['total_businesses'] }},
                totalDownloads: {{ $stats['total_downloads'] }},
                walletBalance: {{ $stats['wallet_balance'] }},
                exportDate: new Date().toISOString()
            };

            const dataStr = JSON.stringify(userData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'account-data-' + new Date().toISOString().split('T')[0] + '.json';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            alert('Account data exported successfully!');
        }
    }

    function confirmDeleteAccount() {
        if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
            if (confirm('This will permanently delete all your data. Are you absolutely sure?')) {
                alert('Account deletion feature will be implemented soon.');
            }
        }
    }
</script>
@endpush

@endsection
