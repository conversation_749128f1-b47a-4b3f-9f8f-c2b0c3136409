@extends('layouts.user')

@section('title', 'Account Dashboard - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', 'Manage your account, profile, businesses, and design projects')

@push('styles')
<style>
    .dashboard-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .dashboard-header .container {
        position: relative;
        z-index: 1;
    }

    .dashboard-header h1 {
        color: white;
        margin-bottom: 0.5rem;
        font-size: 2.5rem;
        font-weight: 700;
    }

    .dashboard-header p {
        opacity: 0.9;
        font-size: 1.125rem;
    }

    .welcome-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .welcome-info h1 {
        margin-bottom: 0.5rem;
    }

    .quick-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .quick-action-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .quick-action-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .stat-card {
        background: white;
        padding: 2rem;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #f1f5f9;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
    }

    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }

    .stat-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 800;
        color: #1e293b;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stat-label {
        color: #64748b;
        font-size: 1rem;
        font-weight: 500;
    }

    .stat-trend {
        font-size: 0.875rem;
        font-weight: 600;
        margin-top: 0.5rem;
    }

    .trend-up {
        color: #10b981;
    }

    .trend-down {
        color: #ef4444;
    }
    
    .dashboard-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 2rem;
        margin-bottom: 3rem;
    }

    @media (min-width: 1024px) {
        .dashboard-grid {
            grid-template-columns: 2fr 1fr;
        }
    }

    .dashboard-section {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #f1f5f9;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .dashboard-section:hover {
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }

    .section-header {
        padding: 2rem;
        border-bottom: 1px solid #f1f5f9;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }

    .section-header h3 {
        margin: 0;
        color: #1e293b;
        font-size: 1.25rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-header h3::before {
        content: '';
        width: 4px;
        height: 20px;
        background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
        border-radius: 2px;
    }

    .section-content {
        padding: 2rem;
    }
    
    .business-list {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .business-item {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        padding: 1.5rem;
        border: 2px solid #f1f5f9;
        border-radius: 12px;
        transition: all 0.3s ease;
        background: #fafbfc;
        position: relative;
    }

    .business-item:hover {
        border-color: var(--primary-color);
        background: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    .business-item.default {
        border-color: #10b981;
        background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
    }

    .business-item.default::before {
        content: '★';
        position: absolute;
        top: -8px;
        right: -8px;
        background: #10b981;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        font-weight: bold;
    }

    .business-logo {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
        overflow: hidden;
        flex-shrink: 0;
        border: 2px solid #e2e8f0;
    }

    .business-logo-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .business-info {
        flex: 1;
    }

    .business-name {
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 1.125rem;
    }

    .business-meta {
        color: #64748b;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .business-meta i {
        color: var(--primary-color);
    }

    .default-badge {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .business-actions {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }
    
    .subscription-card {
        text-align: center;
        padding: 2rem;
    }
    
    .subscription-status {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 1rem;
    }
    
    .status-active {
        background: #dcfce7;
        color: #166534;
    }
    
    .status-inactive {
        background: #fef2f2;
        color: #dc2626;
    }
    
    .wallet-balance {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .transaction-list {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .transaction-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f1f5f9;
    }
    
    .transaction-item:last-child {
        border-bottom: none;
    }
    
    .transaction-info {
        flex: 1;
    }
    
    .transaction-desc {
        font-weight: 500;
        color: #1e293b;
        margin-bottom: 0.25rem;
    }
    
    .transaction-date {
        font-size: 0.875rem;
        color: #64748b;
    }
    
    .transaction-amount {
        font-weight: 600;
    }
    
    .amount-credit {
        color: #059669;
    }
    
    .amount-debit {
        color: #dc2626;
    }
    
    .empty-state {
        text-align: center;
        padding: 2rem;
        color: #64748b;
    }
    
    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #cbd5e1;
    }
    
    @media (max-width: 768px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
        
        .business-item {
            flex-direction: column;
            text-align: center;
        }
    }

    /* Business Modal Styles */
    .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background: white;
        border-radius: 12px;
        width: 90%;
        max-width: 600px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    }

    .modal-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .modal-header h3 {
        margin: 0;
        color: #1f2937;
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #6b7280;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .modal-close:hover {
        background: #f3f4f6;
        color: #374151;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        padding: 1.5rem;
        border-top: 1px solid #e5e7eb;
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
    }

    .form-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.2s ease;
    }

    .form-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-help {
        display: block;
        margin-top: 0.25rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        font-weight: 500;
        color: #374151;
    }

    .business-logo-img {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        object-fit: cover;
    }

    .btn-sm {
        padding: 0.5rem;
        font-size: 0.875rem;
    }

    .btn-danger {
        background: #dc2626;
        border-color: #dc2626;
    }

    .btn-danger:hover {
        background: #b91c1c;
        border-color: #b91c1c;
    }

    /* Profile Form Styles */
    .profile-form-container {
        background: #f8fafc;
        border-radius: 8px;
        padding: 1.5rem;
    }

    .account-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
    }

    .info-item {
        text-align: center;
    }

    .info-label {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 0.25rem;
    }

    .info-value {
        font-weight: 600;
        color: #1f2937;
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 1rem;
    }

    .error-message {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Account Actions Styles */
    .account-actions {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .action-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1.5rem;
        background: white;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
    }

    .action-item.danger {
        border-color: #fecaca;
        background: #fef2f2;
    }

    .action-info h4 {
        margin: 0 0 0.5rem 0;
        color: #1f2937;
    }

    .action-info p {
        margin: 0;
        color: #6b7280;
        font-size: 0.875rem;
    }

    .action-item.danger .action-info h4 {
        color: #dc2626;
    }

    .action-item.danger .action-info p {
        color: #991b1b;
    }
</style>
@endpush

@section('content')
<!-- Dashboard Header -->
<section class="dashboard-header">
    <div class="container">
        <div class="welcome-section">
            <div class="welcome-info">
                <h1>Welcome back, {{ $user->name }}! 👋</h1>
                <p>Ready to create amazing designs? Let's get started with your projects.</p>
            </div>
            <div class="quick-actions">
                <a href="{{ route('user.categories') }}" class="quick-action-btn">
                    <i class="fas fa-plus"></i> Create Design
                </a>
                <a href="{{ route('user.payment.plans') }}" class="quick-action-btn">
                    <i class="fas fa-crown"></i> View Plans
                </a>
                <a href="{{ route('user.downloads') }}" class="quick-action-btn">
                    <i class="fas fa-download"></i> Downloads
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Stats Grid -->
<div class="container">
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-download"></i>
                </div>
            </div>
            <div class="stat-value">{{ $stats['total_downloads'] }}</div>
            <div class="stat-label">Total Downloads</div>
            <div class="stat-trend trend-up">
                <i class="fas fa-arrow-up"></i> All time
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
            </div>
            <div class="stat-value">{{ $stats['this_month_downloads'] }}</div>
            <div class="stat-label">This Month Downloads</div>
            <div class="stat-trend trend-up">
                <i class="fas fa-arrow-up"></i> +{{ $stats['this_month_downloads'] > 0 ? '100' : '0' }}% from last month
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-wallet"></i>
                </div>
            </div>
            <div class="stat-value">₹{{ number_format($stats['wallet_balance'], 2) }}</div>
            <div class="stat-label">Wallet Balance</div>
            <div class="stat-trend">
                <i class="fas fa-info-circle"></i> Available for downloads
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-building"></i>
                </div>
            </div>
            <div class="stat-value">{{ $stats['total_businesses'] }}</div>
            <div class="stat-label">Business Profiles</div>
            <div class="stat-trend">
                <i class="fas fa-check-circle"></i> Ready for designs
            </div>
        </div>
    </div>

    <!-- Dashboard Grid -->
    <div class="dashboard-grid">


        <!-- Business Profiles Section -->
        <div class="dashboard-section">
            <div class="section-header">
                <h3>Business Profiles</h3>
                <button class="btn btn-primary" onclick="openBusinessModal()">
                    <i class="fas fa-plus"></i> Add Business
                </button>
            </div>
            <div class="section-content">
                @if($businesses->count() > 0)
                    <div class="business-list" id="businessList">
                        @foreach($businesses as $business)
                            <div class="business-item {{ $business->is_default ? 'default' : '' }}" data-business-id="{{ $business->id }}">
                                <div class="business-logo">
                                    @if($business->logo_path)
                                        <img src="{{ Storage::url($business->logo_path) }}" alt="{{ $business->name }}" class="business-logo-img">
                                    @else
                                        <i class="fas fa-building"></i>
                                    @endif
                                </div>
                                <div class="business-info">
                                    <div class="business-name">
                                        {{ $business->name }}
                                        @if($business->is_default)
                                            <span class="default-badge">Default</span>
                                        @endif
                                    </div>
                                    <div class="business-meta">
                                        @if($business->email)
                                            {{ $business->email }}
                                        @elseif($business->phone)
                                            {{ $business->phone }}
                                        @else
                                            No contact info
                                        @endif
                                    </div>
                                </div>
                                <div class="business-actions">
                                    @if(!$business->is_default)
                                        <button class="btn btn-outline btn-sm" onclick="setDefaultBusiness({{ $business->id }})" title="Set as Default">
                                            <i class="fas fa-star"></i>
                                        </button>
                                    @endif
                                    <button class="btn btn-outline btn-sm" onclick="editBusiness({{ $business->id }})" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    @if($businesses->count() > 1)
                                        <button class="btn btn-outline btn-sm btn-danger" onclick="deleteBusiness({{ $business->id }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="empty-state" id="emptyState">
                        <i class="fas fa-building"></i>
                        <h4>No Business Profiles</h4>
                        <p>Add your first business profile to get started</p>
                        <button class="btn btn-primary" onclick="openBusinessModal()">
                            <i class="fas fa-plus"></i> Add Business
                        </button>
                    </div>
                @endif
            </div>
        </div>

        <!-- Profile Settings Section -->
        <div class="dashboard-section">
            <div class="section-header">
                <h3>Profile Settings</h3>
            </div>
            <div class="section-content">
                <div class="profile-form-container">
                    <form method="POST" action="{{ route('user.profile.update') }}" class="profile-form">
                        @csrf
                        @method('PUT')

                        <div class="form-row">
                            <div class="form-group">
                                <label for="name" class="form-label">Full Name</label>
                                <input
                                    type="text"
                                    id="name"
                                    name="name"
                                    class="form-input @error('name') error @enderror"
                                    value="{{ old('name', $user->name) }}"
                                    required
                                >
                                @error('name')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="form-group">
                                <label for="email" class="form-label">Email Address</label>
                                <input
                                    type="email"
                                    id="email"
                                    name="email"
                                    class="form-input"
                                    value="{{ $user->email }}"
                                    readonly
                                    style="background-color: #f9fafb; cursor: not-allowed;"
                                >
                                <small class="form-help">Email address cannot be changed for security reasons.</small>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Account Information</label>
                            <div class="account-info-grid">
                                <div class="info-item">
                                    <div class="info-label">Member Since</div>
                                    <div class="info-value">{{ $user->created_at->format('M d, Y') }}</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Days Active</div>
                                    <div class="info-value">{{ $user->created_at->diffInDays() + 1 }} {{ $user->created_at->diffInDays() + 1 === 1 ? 'day' : 'days' }}</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Authentication</div>
                                    <div class="info-value">
                                        @if($user->google_id)
                                            <span style="color: #059669;">
                                                <i class="fab fa-google"></i> Google Account
                                            </span>
                                        @else
                                            <span style="color: #6b7280;">
                                                <i class="fas fa-envelope"></i> Email Account
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Account Actions Section -->
        <div class="dashboard-section">
            <div class="section-header">
                <h3>Account Actions</h3>
            </div>
            <div class="section-content">
                <div class="account-actions">
                    <div class="action-item">
                        <div class="action-info">
                            <h4>Download Account Data</h4>
                            <p>Export all your account data including businesses, downloads, and transactions.</p>
                        </div>
                        <button class="btn btn-outline" onclick="downloadAccountData()">
                            <i class="fas fa-download"></i> Export Data
                        </button>
                    </div>

                    <div class="action-item danger">
                        <div class="action-info">
                            <h4>Delete Account</h4>
                            <p>Permanently delete your account and all associated data. This action cannot be undone.</p>
                        </div>
                        <button class="btn btn-danger" onclick="confirmDeleteAccount()">
                            <i class="fas fa-trash"></i> Delete Account
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div>
            <!-- Subscription Status -->
            <div class="dashboard-section" style="margin-bottom: 1.5rem;">
                <div class="section-header">
                    <h3>Subscription</h3>
                </div>
                <div class="section-content">
                    <div class="subscription-card">
                        @if($activeSubscription)
                            <div class="subscription-status status-active">
                                <i class="fas fa-check-circle"></i>
                                Active
                            </div>
                            <h4>{{ $activeSubscription->plan->name }}</h4>
                            <p>Expires: {{ $activeSubscription->end_date->format('M d, Y') }}</p>
                        @else
                            <div class="subscription-status status-inactive">
                                <i class="fas fa-times-circle"></i>
                                No Active Plan
                            </div>
                            <p>Upgrade to access premium features</p>
                            <a href="#" class="btn btn-primary">View Plans</a>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h3>Recent Transactions</h3>
                </div>
                <div class="section-content">
                    @if($recentTransactions->count() > 0)
                        <div class="transaction-list">
                            @foreach($recentTransactions as $transaction)
                                <div class="transaction-item">
                                    <div class="transaction-info">
                                        <div class="transaction-desc">{{ $transaction->description }}</div>
                                        <div class="transaction-date">{{ $transaction->created_at->format('M d, Y') }}</div>
                                    </div>
                                    <div class="transaction-amount {{ $transaction->type === 'credit' ? 'amount-credit' : 'amount-debit' }}">
                                        {{ $transaction->type === 'credit' ? '+' : '-' }}₹{{ number_format($transaction->amount, 2) }}
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="empty-state">
                            <i class="fas fa-receipt"></i>
                            <h4>No Transactions</h4>
                            <p>Your transaction history will appear here</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Business Modal -->
<div id="businessModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalTitle">Add Business Profile</h3>
            <button type="button" class="modal-close" onclick="closeBusinessModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="businessForm" enctype="multipart/form-data">
            @csrf
            <input type="hidden" id="businessId" name="business_id">
            <input type="hidden" id="formMethod" name="_method" value="POST">

            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="businessName" class="form-label">Business Name *</label>
                        <input type="text" id="businessName" name="name" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="businessSlogan" class="form-label">Slogan</label>
                        <input type="text" id="businessSlogan" name="slogan" class="form-input">
                    </div>
                </div>

                <div class="form-group">
                    <label for="businessAddress" class="form-label">Address</label>
                    <textarea id="businessAddress" name="address" class="form-input" rows="3"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="businessPhone" class="form-label">Phone</label>
                        <input type="text" id="businessPhone" name="phone" class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="businessWhatsapp" class="form-label">WhatsApp</label>
                        <input type="text" id="businessWhatsapp" name="whatsapp" class="form-input">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="businessEmail" class="form-label">Email</label>
                        <input type="email" id="businessEmail" name="email" class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="businessWebsite" class="form-label">Website</label>
                        <input type="url" id="businessWebsite" name="website" class="form-input">
                    </div>
                </div>

                <div class="form-group">
                    <label for="businessLogo" class="form-label">Logo</label>
                    <input type="file" id="businessLogo" name="logo" class="form-input" accept="image/*">
                    <small class="form-help">Upload a logo for your business (JPG, PNG, GIF, WebP - Max 2MB)</small>
                    <div id="logoPreview" style="margin-top: 10px; display: none;">
                        <img id="logoPreviewImg" src="" alt="Logo Preview" style="max-width: 100px; max-height: 100px; border-radius: 8px;">
                    </div>
                </div>

                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="businessIsDefault" name="is_default" value="1">
                        <span class="checkmark"></span>
                        Set as default business
                    </label>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeBusinessModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> <span id="submitText">Save Business</span>
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
    let currentBusinessId = null;

    function openBusinessModal(businessId = null) {
        currentBusinessId = businessId;
        const modal = document.getElementById('businessModal');
        const form = document.getElementById('businessForm');
        const modalTitle = document.getElementById('modalTitle');
        const submitText = document.getElementById('submitText');
        const methodInput = document.getElementById('formMethod');

        // Reset form
        form.reset();
        document.getElementById('logoPreview').style.display = 'none';

        if (businessId) {
            // Edit mode
            modalTitle.textContent = 'Edit Business Profile';
            submitText.textContent = 'Update Business';
            methodInput.value = 'PUT';

            // Load business data
            loadBusinessData(businessId);
        } else {
            // Create mode
            modalTitle.textContent = 'Add Business Profile';
            submitText.textContent = 'Save Business';
            methodInput.value = 'POST';
        }

        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }

    function closeBusinessModal() {
        const modal = document.getElementById('businessModal');
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
        currentBusinessId = null;
    }

    function loadBusinessData(businessId) {
        fetch(`/businesses/${businessId}/edit`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                const business = data.business;
                document.getElementById('businessId').value = business.id;
                document.getElementById('businessName').value = business.name || '';
                document.getElementById('businessSlogan').value = business.slogan || '';
                document.getElementById('businessAddress').value = business.address || '';
                document.getElementById('businessPhone').value = business.phone || '';
                document.getElementById('businessWhatsapp').value = business.whatsapp || '';
                document.getElementById('businessEmail').value = business.email || '';
                document.getElementById('businessWebsite').value = business.website || '';
                document.getElementById('businessIsDefault').checked = business.is_default;

                // Show logo preview if exists
                if (business.logo_url) {
                    document.getElementById('logoPreviewImg').src = business.logo_url;
                    document.getElementById('logoPreview').style.display = 'block';
                }
            }
        })
        .catch(error => {
            console.error('Error loading business data:', error);
            showNotification('Error loading business data', 'error');
        });
    }

    function editBusiness(businessId) {
        openBusinessModal(businessId);
    }

    function setDefaultBusiness(businessId) {
        if (confirm('Set this business as your default?')) {
            fetch(`/businesses/${businessId}/set-default`, {
                method: 'PUT',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification(data.message, 'success');
                    location.reload(); // Reload to update the UI
                } else {
                    showNotification(data.message || 'Error setting default business', 'error');
                }
            })
            .catch(error => {
                console.error('Error setting default business:', error);
                showNotification('Error setting default business', 'error');
            });
        }
    }

    function deleteBusiness(businessId) {
        if (confirm('Are you sure you want to delete this business profile? This action cannot be undone.')) {
            fetch(`/businesses/${businessId}`, {
                method: 'DELETE',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification(data.message, 'success');
                    location.reload(); // Reload to update the UI
                } else {
                    showNotification(data.message || 'Error deleting business', 'error');
                }
            })
            .catch(error => {
                console.error('Error deleting business:', error);
                showNotification('Error deleting business', 'error');
            });
        }
    }

    // Handle form submission
    document.getElementById('businessForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const method = formData.get('_method');
        const url = currentBusinessId ? `/businesses/${currentBusinessId}` : '/businesses';

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        submitBtn.disabled = true;

        fetch(url, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showNotification(data.message, 'success');
                closeBusinessModal();
                location.reload(); // Reload to update the UI
            } else {
                showNotification(data.message || 'Error saving business', 'error');
            }
        })
        .catch(error => {
            console.error('Error saving business:', error);
            showNotification('Error saving business', 'error');
        })
        .finally(() => {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });

    // Handle logo preview
    document.getElementById('businessLogo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('logoPreviewImg').src = e.target.result;
                document.getElementById('logoPreview').style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            document.getElementById('logoPreview').style.display = 'none';
        }
    });

    // Close modal when clicking outside
    document.getElementById('businessModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeBusinessModal();
        }
    });

    // Account action functions
    function downloadAccountData() {
        if (confirm('This will generate and download a file containing all your account data. Continue?')) {
            // Create a simple data export
            const userData = {
                name: '{{ $user->name }}',
                email: '{{ $user->email }}',
                memberSince: '{{ $user->created_at->format('Y-m-d H:i:s') }}',
                totalBusinesses: {{ $stats['total_businesses'] }},
                totalDownloads: {{ $stats['total_downloads'] }},
                walletBalance: {{ $stats['wallet_balance'] }},
                exportDate: new Date().toISOString()
            };

            const dataStr = JSON.stringify(userData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'account-data-' + new Date().toISOString().split('T')[0] + '.json';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            showNotification('Account data exported successfully!', 'success');
        }
    }

    function confirmDeleteAccount() {
        if (confirm('Are you absolutely sure you want to delete your account? This action cannot be undone and will permanently delete all your data including businesses, downloads, and transactions.')) {
            if (confirm('This is your final warning. Type "DELETE" in the next prompt to confirm account deletion.')) {
                const confirmation = prompt('Type "DELETE" to confirm account deletion:');
                if (confirmation === 'DELETE') {
                    deleteAccount();
                } else {
                    alert('Account deletion cancelled. The confirmation text did not match.');
                }
            }
        }
    }

    function deleteAccount() {
        fetch('{{ route('user.account.delete') }}', {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            if (response.ok) {
                alert('Your account has been successfully deleted. You will be redirected to the login page.');
                window.location.href = '{{ route('user.login') }}';
            } else {
                throw new Error('Failed to delete account');
            }
        })
        .catch(error => {
            console.error('Error deleting account:', error);
            showNotification('Error deleting account. Please try again.', 'error');
        });
    }

    // Utility function for notifications
    function showNotification(message, type = 'info') {
        // You can implement a toast notification system here
        // For now, we'll use a simple alert
        if (type === 'error') {
            alert('Error: ' + message);
        } else {
            alert(message);
        }
    }
</script>
@endpush

@endsection


