// Design Editor JavaScript
class DesignEditor {
    constructor() {
        this.canvas = document.getElementById('designCanvas');
        this.selectedElement = null;
        this.isDragging = false;
        this.isResizing = false;
        this.resizeHandle = null;
        this.dragOffset = { x: 0, y: 0 };
        this.resizeStartData = null;
        this.zoomLevel = 1;
        this.history = [];
        this.historyIndex = -1;
        this.currentBusiness = null;

        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadDefaultBusiness();
        this.updateLayersPanel();
        this.saveState();
    }
    
    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.sidebar-tab').forEach(tab => {
            tab.addEventListener('click', () => this.switchTab(tab.dataset.tab));
        });
        
        // Business selector
        document.getElementById('businessSelect').addEventListener('change', (e) => {
            this.loadBusiness(e.target.value);
        });
        
        // Canvas events
        this.canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        document.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        document.addEventListener('mouseup', () => this.handleMouseUp());
        
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // Undo/Redo buttons
        document.getElementById('undoBtn').addEventListener('click', () => this.undo());
        document.getElementById('redoBtn').addEventListener('click', () => this.redo());
        
        // Prevent context menu on canvas
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    }
    
    switchTab(tabId) {
        // Update tab buttons
        document.querySelectorAll('.sidebar-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabId);
        });
        
        // Update tab panels
        document.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.toggle('active', panel.id === tabId);
        });
    }
    
    async loadBusiness(businessId) {
        try {
            const response = await fetch(`/design/business/${businessId}`, {
                headers: {
                    'X-CSRF-TOKEN': window.editorData.csrfToken
                }
            });
            
            if (response.ok) {
                const result = await response.json();
                this.currentBusiness = result.data;
            }
        } catch (error) {
            console.error('Failed to load business:', error);
        }
    }
    
    loadDefaultBusiness() {
        this.currentBusiness = window.editorData.defaultBusiness;
    }
    
    addElement(type) {
        if (!this.currentBusiness && type.startsWith('business-')) {
            alert('Please select a business profile first');
            return;
        }

        const element = this.createElement(type);
        this.canvas.appendChild(element);
        this.selectElement(element);
        this.updateLayersPanel();
        this.saveState();
    }
    
    createElement(type) {
        const element = document.createElement('div');
        element.className = 'design-element';
        element.dataset.type = type;
        element.dataset.id = Date.now().toString();
        
        // Set initial position
        const rect = this.canvas.getBoundingClientRect();
        element.style.left = '50px';
        element.style.top = '50px';
        
        // Create content based on type
        let content = '';
        switch (type) {
            case 'business-name':
                content = `<div class="element-text" style="font-size: 24px; font-weight: bold;">${this.currentBusiness?.name || 'Business Name'}</div>`;
                break;
            case 'business-slogan':
                content = `<div class="element-text" style="font-size: 16px; font-style: italic;">${this.currentBusiness?.slogan || 'Your Slogan Here'}</div>`;
                break;
            case 'business-phone':
                content = `<div class="element-text" style="font-size: 14px;"><i class="fas fa-phone"></i> ${this.currentBusiness?.phone || '+****************'}</div>`;
                break;
            case 'business-email':
                content = `<div class="element-text" style="font-size: 14px;"><i class="fas fa-envelope"></i> ${this.currentBusiness?.email || '<EMAIL>'}</div>`;
                break;
            case 'business-website':
                content = `<div class="element-text" style="font-size: 14px;"><i class="fas fa-globe"></i> ${this.currentBusiness?.website || 'www.business.com'}</div>`;
                break;
            case 'business-address':
                content = `<div class="element-text" style="font-size: 12px;"><i class="fas fa-map-marker-alt"></i> ${this.currentBusiness?.address || 'Business Address'}</div>`;
                break;
            case 'business-logo':
                if (this.currentBusiness?.logo_url) {
                    content = `<img src="${this.currentBusiness.logo_url}" class="element-logo" alt="Business Logo">`;
                } else {
                    content = `<div class="element-logo" style="display: flex; align-items: center; justify-content: center; background: #f1f5f9; color: #64748b;"><i class="fas fa-building fa-2x"></i></div>`;
                }
                break;
            case 'custom-text':
                content = `<div class="element-text" contenteditable="true" style="font-size: 16px;">Custom Text</div>`;
                break;
        }
        
        element.innerHTML = content + '<div class="resize-handle se"></div>';
        
        // Make element draggable
        element.draggable = false; // We'll handle dragging manually
        
        return element;
    }
    
    selectElement(element) {
        // Remove selection from other elements
        document.querySelectorAll('.design-element').forEach(el => {
            el.classList.remove('selected');
            // Hide resize handles from previous elements
            const prevHandles = el.querySelectorAll('.resize-handle');
            prevHandles.forEach(handle => handle.style.display = 'none');
        });

        // Select the new element
        element.classList.add('selected');
        this.selectedElement = element;

        // Show resize handles for the selected element
        const handles = element.querySelectorAll('.resize-handle');
        handles.forEach(handle => {
            handle.style.display = 'block';
            // Add resize event listeners
            handle.addEventListener('mousedown', (e) => this.handleResizeStart(e, handle));
        });

        // Update properties panel
        this.updatePropertiesPanel(element);
    }
    
    updatePropertiesPanel(element) {
        const propertiesContent = document.getElementById('propertiesContent');
        const type = element.dataset.type;
        
        let html = `
            <div class="properties-panel">
                <h4 style="margin-bottom: 1rem;">Element Properties</h4>
                
                <div class="property-group">
                    <label class="property-label">Position</label>
                    <div class="property-row">
                        <input type="number" class="property-input" id="posX" value="${parseInt(element.style.left)}" placeholder="X">
                        <input type="number" class="property-input" id="posY" value="${parseInt(element.style.top)}" placeholder="Y">
                    </div>
                </div>
        `;
        
        if (type !== 'business-logo') {
            const textElement = element.querySelector('.element-text');
            const computedStyle = window.getComputedStyle(textElement);
            const currentOpacity = Math.round((parseFloat(element.style.opacity) || 1) * 100);
            const currentBgColor = computedStyle.backgroundColor;
            const isTransparent = currentBgColor === 'rgba(0, 0, 0, 0)' || currentBgColor === 'transparent';

            html += `
                <div class="property-group">
                    <label class="property-label">Font Size</label>
                    <input type="number" class="property-input" id="fontSize" value="${parseInt(computedStyle.fontSize)}" min="8" max="72">
                </div>

                <div class="property-group">
                    <label class="property-label">Text Color</label>
                    <input type="color" class="color-input" id="textColor" value="${this.rgbToHex(computedStyle.color)}">
                </div>

                <div class="property-group">
                    <label class="property-label">Background</label>
                    <div class="background-controls">
                        <select class="property-input" id="bgType" style="margin-bottom: 0.5rem;">
                            <option value="transparent" ${isTransparent ? 'selected' : ''}>Transparent</option>
                            <option value="color" ${!isTransparent ? 'selected' : ''}>Color</option>
                        </select>
                        <input type="color" class="color-input" id="bgColor" value="${isTransparent ? '#ffffff' : this.rgbToHex(currentBgColor)}" style="display: ${isTransparent ? 'none' : 'block'};">
                    </div>
                </div>

                <div class="property-group">
                    <label class="property-label">Opacity</label>
                    <div class="opacity-controls">
                        <input type="range" class="opacity-slider" id="opacity" min="0" max="100" value="${currentOpacity}" style="width: 100%; margin-bottom: 0.5rem;">
                        <span class="opacity-value" id="opacityValue">${currentOpacity}%</span>
                    </div>
                </div>

                <div class="property-group">
                    <label class="property-label">Font Weight</label>
                    <select class="property-input" id="fontWeight">
                        <option value="normal" ${computedStyle.fontWeight === 'normal' ? 'selected' : ''}>Normal</option>
                        <option value="bold" ${computedStyle.fontWeight === 'bold' || computedStyle.fontWeight === '700' ? 'selected' : ''}>Bold</option>
                    </select>
                </div>
            `;
        }
        
        html += `
                <div class="property-group">
                    <button class="btn btn-outline" onclick="editor.deleteElement()" style="width: 100%; color: #dc2626; border-color: #dc2626;">
                        <i class="fas fa-trash"></i> Delete Element
                    </button>
                </div>
            </div>
        `;
        
        propertiesContent.innerHTML = html;
        
        // Add event listeners for property changes
        this.setupPropertyListeners();
    }
    
    setupPropertyListeners() {
        const posX = document.getElementById('posX');
        const posY = document.getElementById('posY');
        const fontSize = document.getElementById('fontSize');
        const textColor = document.getElementById('textColor');
        const bgColor = document.getElementById('bgColor');
        const bgType = document.getElementById('bgType');
        const opacity = document.getElementById('opacity');
        const fontWeight = document.getElementById('fontWeight');

        if (posX) posX.addEventListener('change', () => this.updateElementPosition());
        if (posY) posY.addEventListener('change', () => this.updateElementPosition());
        if (fontSize) fontSize.addEventListener('change', () => this.updateElementStyle());
        if (textColor) textColor.addEventListener('change', () => this.updateElementStyle());
        if (bgColor) bgColor.addEventListener('change', () => this.updateElementStyle());
        if (bgType) bgType.addEventListener('change', () => this.updateBackgroundType());
        if (opacity) opacity.addEventListener('input', () => this.updateElementOpacity());
        if (fontWeight) fontWeight.addEventListener('change', () => this.updateElementStyle());
    }
    
    updateElementPosition() {
        if (!this.selectedElement) return;
        
        const posX = document.getElementById('posX').value;
        const posY = document.getElementById('posY').value;
        
        this.selectedElement.style.left = posX + 'px';
        this.selectedElement.style.top = posY + 'px';
        
        this.saveState();
    }
    
    updateElementStyle() {
        if (!this.selectedElement) return;

        const textElement = this.selectedElement.querySelector('.element-text');
        if (!textElement) return;

        const fontSize = document.getElementById('fontSize')?.value;
        const textColor = document.getElementById('textColor')?.value;
        const bgColor = document.getElementById('bgColor')?.value;
        const bgType = document.getElementById('bgType')?.value;
        const fontWeight = document.getElementById('fontWeight')?.value;

        if (fontSize) textElement.style.fontSize = fontSize + 'px';
        if (textColor) textElement.style.color = textColor;
        if (bgType === 'transparent') {
            textElement.style.backgroundColor = 'transparent';
        } else if (bgColor) {
            textElement.style.backgroundColor = bgColor;
        }
        if (fontWeight) textElement.style.fontWeight = fontWeight;

        this.saveState();
    }

    updateBackgroundType() {
        if (!this.selectedElement) return;

        const bgType = document.getElementById('bgType')?.value;
        const bgColorInput = document.getElementById('bgColor');

        if (bgType === 'transparent') {
            bgColorInput.style.display = 'none';
            const textElement = this.selectedElement.querySelector('.element-text');
            if (textElement) {
                textElement.style.backgroundColor = 'transparent';
            }
        } else {
            bgColorInput.style.display = 'block';
        }

        this.updateElementStyle();
    }

    updateElementOpacity() {
        if (!this.selectedElement) return;

        const opacity = document.getElementById('opacity')?.value;
        const opacityValue = document.getElementById('opacityValue');

        if (opacity !== undefined) {
            const opacityDecimal = opacity / 100;
            this.selectedElement.style.opacity = opacityDecimal;
            if (opacityValue) {
                opacityValue.textContent = opacity + '%';
            }
        }

        this.saveState();
    }
    
    deleteElement() {
        if (this.selectedElement) {
            this.selectedElement.remove();
            this.selectedElement = null;
            document.getElementById('propertiesContent').innerHTML = '<p style="color: #64748b; text-align: center; padding: 2rem;">Select an element to edit its properties</p>';
            this.updateLayersPanel();
            this.saveState();
        }
    }
    
    handleCanvasClick(e) {
        if (e.target === this.canvas) {
            // Clicked on empty canvas
            this.selectElement(null);
        } else if (e.target.closest('.design-element')) {
            // Clicked on an element
            this.selectElement(e.target.closest('.design-element'));
        }
    }
    
    handleMouseDown(e) {
        const element = e.target.closest('.design-element');
        if (!element) return;
        
        this.isDragging = true;
        this.selectElement(element);
        
        const rect = element.getBoundingClientRect();
        const canvasRect = this.canvas.getBoundingClientRect();
        
        this.dragOffset = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
        
        element.classList.add('dragging');
        e.preventDefault();
    }
    
    handleMouseMove(e) {
        // Handle resizing
        if (this.isResizing) {
            this.handleResize(e);
            return;
        }

        // Handle dragging
        if (!this.isDragging || !this.selectedElement) return;

        const canvasRect = this.canvas.getBoundingClientRect();
        const x = e.clientX - canvasRect.left - this.dragOffset.x;
        const y = e.clientY - canvasRect.top - this.dragOffset.y;

        // Keep element within canvas bounds
        const maxX = this.canvas.offsetWidth - this.selectedElement.offsetWidth;
        const maxY = this.canvas.offsetHeight - this.selectedElement.offsetHeight;

        const clampedX = Math.max(0, Math.min(x, maxX));
        const clampedY = Math.max(0, Math.min(y, maxY));

        this.selectedElement.style.left = clampedX + 'px';
        this.selectedElement.style.top = clampedY + 'px';

        // Update properties panel if open
        const posX = document.getElementById('posX');
        const posY = document.getElementById('posY');
        if (posX) posX.value = clampedX;
        if (posY) posY.value = clampedY;
    }
    
    handleMouseUp() {
        if (this.isDragging) {
            this.isDragging = false;
            if (this.selectedElement) {
                this.selectedElement.classList.remove('dragging');
                this.saveState();
            }
        }

        if (this.isResizing) {
            this.isResizing = false;
            this.resizeHandle = null;
            this.resizeStartData = null;
            if (this.selectedElement) {
                this.saveState();
            }
        }
    }

    handleResizeStart(e, handle) {
        e.preventDefault();
        e.stopPropagation();

        this.isResizing = true;
        this.resizeHandle = handle;

        const element = handle.parentElement;
        const rect = element.getBoundingClientRect();
        const canvasRect = this.canvas.getBoundingClientRect();

        this.resizeStartData = {
            startX: e.clientX,
            startY: e.clientY,
            startWidth: rect.width,
            startHeight: rect.height,
            startLeft: rect.left - canvasRect.left,
            startTop: rect.top - canvasRect.top,
            handleType: handle.className.split(' ')[1] // resize-nw, resize-ne, etc.
        };
    }

    handleResize(e) {
        if (!this.isResizing || !this.resizeHandle || !this.resizeStartData) return;

        const deltaX = e.clientX - this.resizeStartData.startX;
        const deltaY = e.clientY - this.resizeStartData.startY;
        const element = this.resizeHandle.parentElement;
        const handleType = this.resizeStartData.handleType;

        let newWidth = this.resizeStartData.startWidth;
        let newHeight = this.resizeStartData.startHeight;
        let newLeft = this.resizeStartData.startLeft;
        let newTop = this.resizeStartData.startTop;

        // Calculate new dimensions based on handle type
        switch(handleType) {
            case 'resize-se': // Southeast
                newWidth = Math.max(50, this.resizeStartData.startWidth + deltaX);
                newHeight = Math.max(50, this.resizeStartData.startHeight + deltaY);
                break;
            case 'resize-sw': // Southwest
                newWidth = Math.max(50, this.resizeStartData.startWidth - deltaX);
                newHeight = Math.max(50, this.resizeStartData.startHeight + deltaY);
                newLeft = this.resizeStartData.startLeft + deltaX;
                break;
            case 'resize-ne': // Northeast
                newWidth = Math.max(50, this.resizeStartData.startWidth + deltaX);
                newHeight = Math.max(50, this.resizeStartData.startHeight - deltaY);
                newTop = this.resizeStartData.startTop + deltaY;
                break;
            case 'resize-nw': // Northwest
                newWidth = Math.max(50, this.resizeStartData.startWidth - deltaX);
                newHeight = Math.max(50, this.resizeStartData.startHeight - deltaY);
                newLeft = this.resizeStartData.startLeft + deltaX;
                newTop = this.resizeStartData.startTop + deltaY;
                break;
            case 'resize-e': // East
                newWidth = Math.max(50, this.resizeStartData.startWidth + deltaX);
                break;
            case 'resize-w': // West
                newWidth = Math.max(50, this.resizeStartData.startWidth - deltaX);
                newLeft = this.resizeStartData.startLeft + deltaX;
                break;
            case 'resize-s': // South
                newHeight = Math.max(50, this.resizeStartData.startHeight + deltaY);
                break;
            case 'resize-n': // North
                newHeight = Math.max(50, this.resizeStartData.startHeight - deltaY);
                newTop = this.resizeStartData.startTop + deltaY;
                break;
        }

        // Apply new dimensions and position
        element.style.width = newWidth + 'px';
        element.style.height = newHeight + 'px';
        element.style.left = newLeft + 'px';
        element.style.top = newTop + 'px';
    }
    
    handleKeyDown(e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'z':
                    e.preventDefault();
                    if (e.shiftKey) {
                        this.redo();
                    } else {
                        this.undo();
                    }
                    break;
                case 'y':
                    e.preventDefault();
                    this.redo();
                    break;
            }
        }
        
        if (e.key === 'Delete' && this.selectedElement) {
            this.deleteElement();
        }
    }
    
    saveState() {
        const state = {
            elements: Array.from(this.canvas.querySelectorAll('.design-element')).map(el => ({
                id: el.dataset.id,
                type: el.dataset.type,
                style: el.style.cssText,
                innerHTML: el.innerHTML
            }))
        };
        
        // Remove future history if we're not at the end
        this.history = this.history.slice(0, this.historyIndex + 1);
        this.history.push(JSON.stringify(state));
        this.historyIndex++;
        
        // Limit history size
        if (this.history.length > 50) {
            this.history.shift();
            this.historyIndex--;
        }
        
        this.updateHistoryButtons();
    }
    
    undo() {
        if (this.historyIndex > 0) {
            this.historyIndex--;
            this.restoreState();
        }
    }
    
    redo() {
        if (this.historyIndex < this.history.length - 1) {
            this.historyIndex++;
            this.restoreState();
        }
    }
    
    restoreState() {
        const state = JSON.parse(this.history[this.historyIndex]);
        
        // Clear canvas
        this.canvas.querySelectorAll('.design-element').forEach(el => el.remove());
        
        // Restore elements
        state.elements.forEach(elementData => {
            const element = document.createElement('div');
            element.className = 'design-element';
            element.dataset.id = elementData.id;
            element.dataset.type = elementData.type;
            element.style.cssText = elementData.style;
            element.innerHTML = elementData.innerHTML;
            this.canvas.appendChild(element);
        });
        
        this.selectedElement = null;
        this.updateHistoryButtons();
    }
    
    updateHistoryButtons() {
        document.getElementById('undoBtn').disabled = this.historyIndex <= 0;
        document.getElementById('redoBtn').disabled = this.historyIndex >= this.history.length - 1;
    }
    
    rgbToHex(rgb) {
        const result = rgb.match(/\d+/g);
        if (!result) return '#000000';
        return '#' + result.map(x => parseInt(x).toString(16).padStart(2, '0')).join('');
    }

    updateLayersPanel() {
        const layersList = document.getElementById('layersList');
        if (!layersList) return;

        const elements = Array.from(this.canvas.querySelectorAll('.design-element'));

        // Sort elements by z-index (highest first)
        elements.sort((a, b) => {
            const aIndex = parseInt(a.style.zIndex) || 0;
            const bIndex = parseInt(b.style.zIndex) || 0;
            return bIndex - aIndex;
        });

        layersList.innerHTML = '';

        elements.forEach((element, index) => {
            const layerItem = this.createLayerItem(element, index);
            layersList.appendChild(layerItem);
        });
    }

    createLayerItem(element, index) {
        const layerItem = document.createElement('div');
        layerItem.className = 'layer-item';
        layerItem.dataset.elementId = element.dataset.id;

        const type = element.dataset.type;
        const isVisible = element.style.display !== 'none';

        // Get layer name and icon
        let layerName = 'Unknown';
        let layerIcon = 'fas fa-square';

        switch(type) {
            case 'frame':
                layerName = 'Frame';
                layerIcon = 'fas fa-border-style';
                break;
            case 'business-name':
                layerName = 'Business Name';
                layerIcon = 'fas fa-building';
                break;
            case 'business-slogan':
                layerName = 'Business Slogan';
                layerIcon = 'fas fa-quote-left';
                break;
            case 'business-phone':
                layerName = 'Phone Number';
                layerIcon = 'fas fa-phone';
                break;
            case 'business-email':
                layerName = 'Email Address';
                layerIcon = 'fas fa-envelope';
                break;
            case 'business-website':
                layerName = 'Website';
                layerIcon = 'fas fa-globe';
                break;
            case 'business-address':
                layerName = 'Address';
                layerIcon = 'fas fa-map-marker-alt';
                break;
            case 'business-logo':
                layerName = 'Business Logo';
                layerIcon = 'fas fa-image';
                break;
            case 'custom-text':
                layerName = 'Custom Text';
                layerIcon = 'fas fa-font';
                break;
        }

        layerItem.innerHTML = `
            <div class="layer-icon">
                <i class="${layerIcon}"></i>
            </div>
            <div class="layer-info">
                <div class="layer-name">${layerName}</div>
                <div class="layer-type">${type.replace('-', ' ')}</div>
            </div>
            <div class="layer-actions">
                <button class="layer-action" onclick="editor.toggleLayerVisibility('${element.dataset.id}')" title="${isVisible ? 'Hide' : 'Show'} Layer">
                    <i class="fas fa-eye${isVisible ? '' : '-slash'} ${isVisible ? '' : 'visibility-off'}"></i>
                </button>
                <button class="layer-action" onclick="editor.selectElementById('${element.dataset.id}')" title="Select Layer">
                    <i class="fas fa-mouse-pointer"></i>
                </button>
            </div>
        `;

        // Add click handler for layer selection
        layerItem.addEventListener('click', (e) => {
            if (!e.target.closest('.layer-action')) {
                this.selectElementById(element.dataset.id);
            }
        });

        // Add drag and drop for reordering
        layerItem.draggable = true;
        layerItem.addEventListener('dragstart', (e) => this.handleLayerDragStart(e, element));
        layerItem.addEventListener('dragover', (e) => this.handleLayerDragOver(e));
        layerItem.addEventListener('drop', (e) => this.handleLayerDrop(e));

        return layerItem;
    }

    selectElementById(elementId) {
        const element = this.canvas.querySelector(`[data-id="${elementId}"]`);
        if (element) {
            this.selectElement(element);
            this.updateLayersPanel();
        }
    }

    toggleLayerVisibility(elementId) {
        const element = this.canvas.querySelector(`[data-id="${elementId}"]`);
        if (element) {
            const isVisible = element.style.display !== 'none';
            element.style.display = isVisible ? 'none' : 'block';
            this.updateLayersPanel();
            this.saveState();
        }
    }

    bringToFront() {
        if (!this.selectedElement) return;

        const elements = Array.from(this.canvas.querySelectorAll('.design-element'));
        const maxZIndex = Math.max(...elements.map(el => parseInt(el.style.zIndex) || 0));

        this.selectedElement.style.zIndex = maxZIndex + 1;
        this.updateLayersPanel();
        this.saveState();
    }

    sendToBack() {
        if (!this.selectedElement) return;

        const elements = Array.from(this.canvas.querySelectorAll('.design-element'));
        const minZIndex = Math.min(...elements.map(el => parseInt(el.style.zIndex) || 0));

        this.selectedElement.style.zIndex = minZIndex - 1;
        this.updateLayersPanel();
        this.saveState();
    }

    handleLayerDragStart(e, element) {
        e.dataTransfer.setData('text/plain', element.dataset.id);
        e.target.classList.add('dragging');
    }

    handleLayerDragOver(e) {
        e.preventDefault();
    }

    handleLayerDrop(e) {
        e.preventDefault();
        const draggedElementId = e.dataTransfer.getData('text/plain');
        const draggedElement = this.canvas.querySelector(`[data-id="${draggedElementId}"]`);
        const targetLayerItem = e.target.closest('.layer-item');

        if (draggedElement && targetLayerItem) {
            const targetElementId = targetLayerItem.dataset.elementId;
            const targetElement = this.canvas.querySelector(`[data-id="${targetElementId}"]`);

            if (targetElement && draggedElement !== targetElement) {
                // Swap z-index values
                const draggedZIndex = parseInt(draggedElement.style.zIndex) || 0;
                const targetZIndex = parseInt(targetElement.style.zIndex) || 0;

                draggedElement.style.zIndex = targetZIndex;
                targetElement.style.zIndex = draggedZIndex;

                this.updateLayersPanel();
                this.saveState();
            }
        }

        // Remove dragging class
        document.querySelectorAll('.layer-item.dragging').forEach(item => {
            item.classList.remove('dragging');
        });
    }
}

// Global functions
function addElement(type) {
    window.editor.addElement(type);
}

function changeFrame(frameId) {
    const canvas = document.getElementById('designCanvas');

    // Remove existing frame if any
    const existingFrame = canvas.querySelector('.canvas-frame');
    if (existingFrame) {
        existingFrame.remove();
    }

    // If frameId is null, just remove the frame
    if (frameId === null) {
        return;
    }

    // Find the frame data
    const frameData = window.editorData.frames.find(frame => frame.id === frameId);
    if (!frameData) {
        console.error('Frame not found:', frameId);
        return;
    }

    // Create frame element
    const frameElement = document.createElement('div');
    frameElement.className = 'canvas-frame design-element';
    frameElement.dataset.type = 'frame';
    frameElement.dataset.id = 'frame-' + Date.now().toString();
    frameElement.dataset.frameId = frameId;

    frameElement.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: auto;
        z-index: 1000;
        background-image: url('${frameData.image_url}');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        cursor: move;
        border: 2px solid transparent;
    `;

    // Add resize handles
    const resizeHandles = ['nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'];
    resizeHandles.forEach(handle => {
        const resizeHandle = document.createElement('div');
        resizeHandle.className = `resize-handle resize-${handle}`;
        resizeHandle.style.cssText = `
            position: absolute;
            background: #3b82f6;
            border: 2px solid white;
            border-radius: 50%;
            width: 12px;
            height: 12px;
            cursor: ${handle.includes('n') || handle.includes('s') ?
                (handle.includes('w') || handle.includes('e') ?
                    (handle.includes('nw') || handle.includes('se') ? 'nw-resize' : 'ne-resize')
                    : 'ns-resize')
                : (handle.includes('w') || handle.includes('e') ? 'ew-resize' : 'move')};
            display: none;
            z-index: 1001;
        `;

        // Position handles
        switch(handle) {
            case 'nw': resizeHandle.style.cssText += 'top: -6px; left: -6px;'; break;
            case 'ne': resizeHandle.style.cssText += 'top: -6px; right: -6px;'; break;
            case 'sw': resizeHandle.style.cssText += 'bottom: -6px; left: -6px;'; break;
            case 'se': resizeHandle.style.cssText += 'bottom: -6px; right: -6px;'; break;
            case 'n': resizeHandle.style.cssText += 'top: -6px; left: 50%; transform: translateX(-50%);'; break;
            case 's': resizeHandle.style.cssText += 'bottom: -6px; left: 50%; transform: translateX(-50%);'; break;
            case 'e': resizeHandle.style.cssText += 'right: -6px; top: 50%; transform: translateY(-50%);'; break;
            case 'w': resizeHandle.style.cssText += 'left: -6px; top: 50%; transform: translateY(-50%);'; break;
        }

        frameElement.appendChild(resizeHandle);
    });

    // Add frame to canvas
    canvas.appendChild(frameElement);

    // Make frame selectable and draggable
    frameElement.addEventListener('click', (e) => {
        e.stopPropagation();
        if (window.editor) {
            window.editor.selectElement(frameElement);
        }
    });

    // Save state
    if (window.editor) {
        window.editor.saveState();
    }

    console.log('Frame applied:', frameData.name);
}

function zoomIn() {
    window.editor.zoomLevel = Math.min(window.editor.zoomLevel + 0.1, 2);
    updateZoom();
}

function zoomOut() {
    window.editor.zoomLevel = Math.max(window.editor.zoomLevel - 0.1, 0.5);
    updateZoom();
}

function updateZoom() {
    const canvas = document.getElementById('designCanvas');
    canvas.style.transform = `scale(${window.editor.zoomLevel})`;
    document.getElementById('zoomLevel').textContent = Math.round(window.editor.zoomLevel * 100) + '%';
}

async function saveDesign() {
    const designData = {
        elements: Array.from(document.querySelectorAll('.design-element')).map(el => ({
            id: el.dataset.id,
            type: el.dataset.type,
            style: el.style.cssText,
            innerHTML: el.innerHTML
        })),
        template_id: window.editorData.template?.id,
        template_type: window.editorData.type,
        business_id: document.getElementById('businessSelect').value
    };
    
    try {
        const response = await fetch('/design/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.editorData.csrfToken
            },
            body: JSON.stringify({
                design_data: JSON.stringify(designData),
                template_id: designData.template_id,
                template_type: designData.template_type,
                business_id: designData.business_id
            })
        });
        
        const result = await response.json();
        if (result.status === 'success') {
            alert('Design saved successfully!');
        } else {
            alert('Failed to save design: ' + result.message);
        }
    } catch (error) {
        console.error('Save error:', error);
        alert('Failed to save design');
    }
}

async function downloadDesign() {
    const designData = {
        elements: Array.from(document.querySelectorAll('.design-element')).map(el => ({
            id: el.dataset.id,
            type: el.dataset.type,
            style: el.style.cssText,
            innerHTML: el.innerHTML
        })),
        template_id: window.editorData.template?.id,
        template_type: window.editorData.type,
        business_id: document.getElementById('businessSelect')?.value
    };

    try {
        // First check download eligibility
        const eligibilityResponse = await fetch('/user/payment/download/check', {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': window.editorData.csrfToken,
                'Accept': 'application/json'
            }
        });

        const eligibility = await eligibilityResponse.json();

        if (eligibility.status === 'success') {
            const options = eligibility.eligibility;

            if (options.can_download_subscription && options.has_active_subscription) {
                // User has active subscription - direct download
                processDownload(designData);
            } else if (options.can_download_free) {
                // Show download options modal
                showDownloadOptionsModal(designData, options);
            } else if (options.can_download_paid) {
                // Only paid download available
                initiatePaymentDownload(designData, options.download_cost);
            } else {
                alert('Downloads are currently disabled. Please contact support.');
            }
        }
    } catch (error) {
        console.error('Download error:', error);
        alert('Failed to check download eligibility');
    }
}

function showDownloadOptionsModal(designData, options) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed; top: 0; left: 0; right: 0; bottom: 0;
        background: rgba(0,0,0,0.5); z-index: 1000;
        display: flex; align-items: center; justify-content: center;
    `;

    modal.innerHTML = `
        <div style="background: white; border-radius: 12px; padding: 2rem; max-width: 400px; width: 90%;">
            <h3 style="margin-bottom: 1rem; text-align: center;">Choose Download Option</h3>

            ${options.can_download_free ? `
                <button onclick="processDownload(${JSON.stringify(designData).replace(/"/g, '&quot;')}); closeModal()"
                        style="width: 100%; padding: 12px; margin-bottom: 1rem; background: #10b981; color: white; border: none; border-radius: 6px; cursor: pointer;">
                    <i class="fas fa-gift"></i> Free Download
                </button>
            ` : ''}

            ${options.can_download_paid ? `
                <button onclick="initiatePaymentDownload(${JSON.stringify(designData).replace(/"/g, '&quot;')}, '${options.download_cost}'); closeModal()"
                        style="width: 100%; padding: 12px; margin-bottom: 1rem; background: var(--primary-color); color: white; border: none; border-radius: 6px; cursor: pointer;">
                    <i class="fas fa-credit-card"></i> Pay ₹${options.download_cost} & Download
                </button>
            ` : ''}

            ${options.can_download_subscription ? `
                <a href="/payment/plans"
                   style="display: block; width: 100%; padding: 12px; margin-bottom: 1rem; background: #f59e0b; color: white; border: none; border-radius: 6px; text-decoration: none; text-align: center;">
                    <i class="fas fa-crown"></i> Get Subscription
                </a>
            ` : ''}

            <button onclick="closeModal()"
                    style="width: 100%; padding: 8px; background: #e5e7eb; color: #374151; border: none; border-radius: 6px; cursor: pointer;">
                Cancel
            </button>
        </div>
    `;

    document.body.appendChild(modal);
    window.currentModal = modal;
}

function closeModal() {
    if (window.currentModal) {
        document.body.removeChild(window.currentModal);
        window.currentModal = null;
    }
}

async function processDownload(designData) {
    try {
        const response = await fetch('/user/design/download', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.editorData.csrfToken,
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                design_data: JSON.stringify(designData),
                template_id: designData.template_id,
                template_type: designData.template_type,
                business_id: designData.business_id,
                format: 'png',
                quality: 90
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            alert(result.message);
            if (result.download_url) {
                // Create a temporary link to download the file
                const link = document.createElement('a');
                link.href = result.download_url;
                link.download = result.file_name || 'design.png';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        } else {
            alert('Download failed: ' + result.message);
        }
    } catch (error) {
        console.error('Download error:', error);
        alert('Failed to download design. Please try again.');
    }
}

async function initiatePaymentDownload(designData, cost) {
    try {
        // Create payment order
        const response = await fetch('/user/payment/download/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.editorData.csrfToken,
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                design_data: JSON.stringify(designData),
                template_id: designData.template_id,
                template_type: designData.template_type
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            // Open Razorpay checkout
            const options = {
                key: result.key,
                amount: result.amount,
                currency: result.currency,
                name: 'Social Media Post Creator',
                description: 'Download Payment',
                order_id: result.order_id,
                handler: function(response) {
                    verifyDownloadPayment(response, designData);
                },
                theme: {
                    color: getComputedStyle(document.documentElement).getPropertyValue('--primary-color')
                }
            };

            const rzp = new Razorpay(options);
            rzp.open();
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        console.error('Payment error:', error);
        alert('Failed to initiate payment');
    }
}

async function verifyDownloadPayment(response, designData) {
    try {
        const verifyResponse = await fetch('/user/payment/download/verify', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.editorData.csrfToken,
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                razorpay_order_id: response.razorpay_order_id,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_signature: response.razorpay_signature,
                design_data: JSON.stringify(designData)
            })
        });

        const result = await verifyResponse.json();

        if (result.status === 'success') {
            alert(result.message);
            // Process the actual download
            processDownload(designData);
        } else {
            alert('Payment verification failed: ' + result.message);
        }
    } catch (error) {
        console.error('Verification error:', error);
        alert('Payment verification failed');
    }
}

// Initialize editor when page loads
document.addEventListener('DOMContentLoaded', function() {
    window.editor = new DesignEditor();
});
